<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Volume Shader Test - Advanced GPU Performance Analysis Tool</title>
    <meta name="description" content="Comprehensive volume shader testing platform for advanced GPU performance analysis, featuring real-time ray marching, fractal rendering, and detailed performance metrics.">
    <meta name="keywords" content="volume shader,ray marching,GPU test,WebGL,fractal rendering,performance analysis">
    <meta name="author" content="Toxic Mushroom Test Team">
    <link rel="canonical" href="https://volumeshaderbmtest.com/volume-shader-test.html">
    <link rel="icon" href="logo.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="logo.svg">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://volumeshaderbmtest.com/volume-shader-test.html">
    <meta property="og:title" content="Volume Shader Test - Advanced GPU Performance Analysis">
    <meta property="og:description" content="Professional volume shader testing platform with real-time ray marching and fractal rendering capabilities.">
    <meta property="og:image" content="https://volumeshaderbmtest.com/images/volume-shader-og.jpg">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://volumeshaderbmtest.com/volume-shader-test.html">
    <meta property="twitter:title" content="Volume Shader Test - Advanced GPU Performance Analysis">
    <meta property="twitter:description" content="Professional volume shader testing platform with real-time ray marching and fractal rendering capabilities.">
    <meta property="twitter:image" content="https://volumeshaderbmtest.com/images/volume-shader-twitter.jpg">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "Volume Shader Test",
        "applicationCategory": "UtilitiesApplication",
        "operatingSystem": "Web Browser",
        "url": "https://volumeshaderbmtest.com/volume-shader-test.html",
        "description": "Advanced volume shader testing platform featuring real-time ray marching, fractal rendering, and comprehensive GPU performance analysis with detailed metrics and visualization.",
        "featureList": [
            "Real-time Ray Marching",
            "Fractal Volume Rendering",
            "Advanced Shader Complexity Testing",
            "Performance Metrics Analysis",
            "Temperature Monitoring",
            "Stability Assessment",
            "Custom Parameter Control",
            "Export Test Results"
        ],
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
        },
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.9",
            "reviewCount": "850",
            "bestRating": "5",
            "worstRating": "1"
        }
    }
    </script>
    
    <script src="static/js/js.js"></script>
    <script src="static/js/chart.js"></script>
    <script src="static/js/three.min.js"></script>
    <link rel="stylesheet" href="static/css/all.min.css">
    <link href="static/css/css2.css" rel="stylesheet">
    <link rel="stylesheet" href="static/css/style.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                container: {
                    center: true,
                    padding: '1rem',
                    screens: {
                        sm: '640px',
                        md: '768px',
                        lg: '1024px',
                        xl: '1200px',
                        '2xl': '1400px'
                    },
                },
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        secondary: {
                            50: '#fdf4ff',
                            100: '#fae8ff',
                            200: '#f5d0fe',
                            300: '#f0abfc',
                            400: '#e879f9',
                            500: '#d946ef',
                            600: '#c026d3',
                            700: '#a21caf',
                            800: '#86198f',
                            900: '#701a75',
                        },
                        accent: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        },
                        cyber: {
                            50: '#f0fdff',
                            100: '#ccfbf1',
                            200: '#99f6e4',
                            300: '#5eead4',
                            400: '#2dd4bf',
                            500: '#14b8a6',
                            600: '#0d9488',
                            700: '#0f766e',
                            800: '#115e59',
                            900: '#134e4a',
                        },
                        danger: '#ef4444',
                        warning: '#f59e0b',
                        success: '#10b981',
                    },
                    fontFamily: {
                        sans: ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
                        mono: ['JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace']
                    },
                    animation: {
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce-slow': 'bounce 2s infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'float': 'float 6s ease-in-out infinite',
                        'scan': 'scan 2s linear infinite',
                    },
                    keyframes: {
                        glow: {
                            '0%': { boxShadow: '0 0 5px rgba(59, 130, 246, 0.5)' },
                            '100%': { boxShadow: '0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.4)' }
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' }
                        },
                        scan: {
                            '0%': { transform: 'translateX(-100%)' },
                            '100%': { transform: 'translateX(100%)' }
                        }
                    },
                    backdropBlur: {
                        xs: '2px',
                    }
                }
            }
        }
    </script>

    <!-- Custom Styles for Enhanced UI -->
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

        /* Cyber Grid Background */
        .cyber-grid {
            background-image:
                linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: grid-move 20s linear infinite;
        }

        @keyframes grid-move {
            0% { background-position: 0 0; }
            100% { background-position: 50px 50px; }
        }

        /* Glassmorphism Effect */
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .dark .glass {
            background: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Neon Glow Effects */
        .neon-border {
            box-shadow:
                0 0 5px rgba(59, 130, 246, 0.5),
                0 0 10px rgba(59, 130, 246, 0.3),
                0 0 15px rgba(59, 130, 246, 0.2);
        }

        .neon-text {
            text-shadow:
                0 0 5px rgba(59, 130, 246, 0.8),
                0 0 10px rgba(59, 130, 246, 0.6),
                0 0 15px rgba(59, 130, 246, 0.4);
        }

        /* Holographic Effect */
        .holographic {
            background: linear-gradient(45deg,
                rgba(59, 130, 246, 0.1) 0%,
                rgba(147, 51, 234, 0.1) 25%,
                rgba(236, 72, 153, 0.1) 50%,
                rgba(59, 130, 246, 0.1) 75%,
                rgba(147, 51, 234, 0.1) 100%);
            background-size: 400% 400%;
            animation: holographic-shift 4s ease-in-out infinite;
        }

        @keyframes holographic-shift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        /* Scan Line Effect */
        .scan-line {
            position: relative;
            overflow: hidden;
        }

        .scan-line::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(59, 130, 246, 0.3),
                transparent);
            animation: scan 3s linear infinite;
        }

        /* Enhanced Card Styles */
        .cyber-card {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.1) 0%,
                rgba(255, 255, 255, 0.05) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .dark .cyber-card {
            background: linear-gradient(135deg,
                rgba(0, 0, 0, 0.3) 0%,
                rgba(0, 0, 0, 0.1) 100%);
            border: 1px solid rgba(59, 130, 246, 0.3);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.05);
        }

        /* Particle Animation */
        .particles {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(59, 130, 246, 0.6);
            border-radius: 50%;
            animation: float-particle 8s linear infinite;
        }

        @keyframes float-particle {
            0% {
                transform: translateY(100vh) translateX(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-10vh) translateX(100px);
                opacity: 0;
            }
        }

        /* Enhanced Button Styles */
        .btn-cyber {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
            border: 1px solid rgba(59, 130, 246, 0.3);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-cyber:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(147, 51, 234, 0.2));
            border-color: rgba(59, 130, 246, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.2);
        }

        .btn-cyber::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-cyber:hover::before {
            left: 100%;
        }

        /* Status Indicators */
        .status-online {
            position: relative;
        }

        .status-online::before {
            content: '';
            position: absolute;
            top: 50%;
            left: -12px;
            transform: translateY(-50%);
            width: 6px;
            height: 6px;
            background: #22c55e;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        /* Gradient Text Animation */
        .gradient-text-animated {
            background: linear-gradient(-45deg, #3b82f6, #8b5cf6, #ec4899, #3b82f6);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradient-shift 3s ease infinite;
        }

        @keyframes gradient-shift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        /* Card Hover Effects */
        .hover-lift {
            transition: all 0.3s ease;
        }

        .hover-lift:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .dark .hover-lift:hover {
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        /* Loading Spinner */
        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(59, 130, 246, 0.1);
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Mobile-First Responsive Design */
        @media (max-width: 768px) {
            /* Mobile Hero Adjustments */
            .mobile-hero-title {
                font-size: 2.5rem;
                line-height: 1.1;
            }

            .mobile-hero-subtitle {
                font-size: 1.125rem;
                line-height: 1.5;
            }

            /* Mobile Navigation */
            .mobile-nav-enhanced {
                backdrop-filter: blur(20px);
                background: rgba(0, 0, 0, 0.9);
            }

            /* Mobile Cards */
            .mobile-card {
                margin: 1rem;
                padding: 1.5rem;
                border-radius: 1rem;
            }

            /* Mobile Spacing */
            .mobile-section-spacing {
                margin-top: 3rem;
                margin-bottom: 2rem;
            }

            /* Mobile Canvas */
            .mobile-canvas-container {
                height: 50vh !important;
                min-height: 400px !important;
                margin: 0 -1.5rem;
                border-radius: 0;
            }

            /* Mobile Canvas Full Width */
            @media (max-width: 768px) {
                .mobile-canvas-full {
                    margin-left: calc(-50vw + 50%);
                    margin-right: calc(-50vw + 50%);
                    width: 100vw;
                }


            }

            /* Mobile Stats Grid */
            .mobile-stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 0.75rem;
            }

            /* Mobile Button Adjustments */
            .mobile-cta-button {
                width: 100%;
                padding: 1rem 2rem;
                font-size: 1.125rem;
                margin-bottom: 1rem;
            }

            /* Mobile Typography */
            .mobile-section-title {
                font-size: 2rem;
                margin-bottom: 1rem;
            }

            .mobile-section-subtitle {
                font-size: 0.875rem;
                margin-bottom: 2rem;
            }
        }

        /* Tablet Adjustments */
        @media (min-width: 769px) and (max-width: 1024px) {
            .tablet-adjustments {
                padding: 2rem;
            }
        }
    </style>
</head>
<body class="min-h-screen font-sans text-gray-800 dark:text-gray-200 overflow-x-hidden">
    <!-- Advanced Background System -->
    <div class="fixed inset-0 -z-20">
        <!-- Base Gradient -->
        <div class="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-blue-950 dark:to-indigo-950"></div>

        <!-- Cyber Grid -->
        <div class="absolute inset-0 cyber-grid opacity-30 dark:opacity-20"></div>

        <!-- Floating Particles -->
        <div class="particles">
            <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
            <div class="particle" style="left: 20%; animation-delay: 1s;"></div>
            <div class="particle" style="left: 30%; animation-delay: 2s;"></div>
            <div class="particle" style="left: 40%; animation-delay: 3s;"></div>
            <div class="particle" style="left: 50%; animation-delay: 4s;"></div>
            <div class="particle" style="left: 60%; animation-delay: 5s;"></div>
            <div class="particle" style="left: 70%; animation-delay: 6s;"></div>
            <div class="particle" style="left: 80%; animation-delay: 7s;"></div>
            <div class="particle" style="left: 90%; animation-delay: 8s;"></div>
        </div>

        <!-- Holographic Overlay -->
        <div class="absolute inset-0 holographic opacity-20"></div>

        <!-- Radial Gradients -->
        <div class="absolute top-0 left-1/4 w-96 h-96 bg-gradient-radial from-primary-400/20 to-transparent rounded-full blur-3xl"></div>
        <div class="absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-radial from-secondary-400/20 to-transparent rounded-full blur-3xl"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-radial from-cyber-400/10 to-transparent rounded-full blur-3xl"></div>
    </div>
    
    <!-- Header Navigation Bar -->
    <header class="sticky top-0 z-50 backdrop-blur-xl bg-white/10 dark:bg-black/10 border-b border-white/20 dark:border-white/10">
        <div class="container mx-auto px-6 py-4">
            <div class="flex justify-between items-center">
                <!-- Mobile-Optimized Logo Section -->
                <div class="flex items-center space-x-3 md:space-x-4">
                    <div class="relative">
                        <div class="w-8 h-8 md:w-10 md:h-10 bg-gradient-to-br from-primary-400 to-secondary-500 rounded-lg flex items-center justify-center neon-border">
                            <i class="fas fa-cube text-white text-sm md:text-lg animate-pulse-slow"></i>
                        </div>
                        <div class="absolute -inset-1 bg-gradient-to-r from-primary-400 to-secondary-500 rounded-lg blur opacity-30 animate-glow"></div>
                    </div>
                    <div class="flex flex-col md:block">
                        <h1 class="text-lg md:text-xl font-bold bg-gradient-to-r from-primary-400 to-secondary-500 bg-clip-text text-transparent leading-tight">
                            Volume Shader Test
                        </h1>
                        <div class="text-xs text-gray-500 dark:text-gray-400 font-mono hidden md:block">Advanced GPU Analysis</div>
                    </div>
                </div>
            <nav class="nav-menu hidden md:block">
                <ul class="flex flex-wrap gap-x-6 gap-y-2">
                    <li><a href="index.html" class="nav-link">Home</a></li>
                    <li><a href="#test" class="active nav-link">Volume Test</a></li>
                    <li><a href="#technical-details" class="nav-link">Technical Details</a></li>
                    <li><a href="#performance-guide" class="nav-link">Performance Guide</a></li>
                    <li><a href="#shader-analysis" class="nav-link">Shader Analysis</a></li>
                    <li><a href="#faq" class="nav-link">FAQ</a></li>
                </ul>
            </nav>
            <!-- Mobile-Optimized Control Panel -->
            <div class="flex items-center space-x-2 md:space-x-4">
                <!-- Language Selector - Hidden on Mobile -->
                <div class="language-selector relative hidden md:block">
                    <button class="flex items-center space-x-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="language-toggle" title="Switch Language">
                        <i class="fas fa-globe"></i>
                        <span class="text-sm">English</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div class="language-dropdown absolute right-0 mt-2 w-40 bg-white dark:bg-gray-800 rounded-md shadow-lg overflow-hidden z-20 hidden">
                        <a href="volume-shader-test.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 bg-gray-100 dark:bg-gray-700">English</a>
                        <a href="zh-cn/volume-shader-test.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">中文</a>
                        <a href="ja-jp/volume-shader-test.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">日本語</a>
                    </div>
                </div>

                <!-- Theme Toggle -->
                <div class="theme-toggle rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="theme-toggle" title="Switch Theme">
                    <i class="fas fa-moon text-sm md:text-base"></i>
                </div>

                <!-- Mobile Menu -->
                <div class="mobile-menu-btn md:hidden rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="mobile-menu-btn" title="Menu">
                    <i class="fas fa-bars text-sm"></i>
                </div>
            </div>
        </div>
        <!-- Mobile Navigation Menu (Hidden by default) -->
        <div class="mobile-nav hidden bg-white dark:bg-gray-900 shadow-md w-full absolute left-0 top-full border-t border-gray-200 dark:border-gray-700 py-3 px-4 md:hidden">
            <ul class="flex flex-col space-y-3">
                <li><a href="index.html" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Home</a></li>
                <li><a href="#test" class="active block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Volume Test</a></li>
                <li><a href="#technical-details" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Technical Details</a></li>
                <li><a href="#performance-guide" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Performance Guide</a></li>
                <li><a href="#shader-analysis" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Shader Analysis</a></li>
                <li><a href="#faq" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">FAQ</a></li>
            </ul>
        </div>

        <!-- Mobile-Enhanced Test Interface -->
        <div id="test" class="relative mobile-section-spacing md:mt-20">
            <!-- Mobile-Optimized Section Header -->
            <div class="text-center mb-8 md:mb-12 px-4">
                <h2 class="mobile-section-title md:text-4xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent mb-2 md:mb-4">
                    VOLUME SHADER TESTING INTERFACE
                </h2>
                <div class="w-24 md:w-32 h-1 bg-gradient-to-r from-primary-400 to-secondary-400 mx-auto rounded-full mb-2"></div>
                <div class="mobile-section-subtitle md:text-sm text-gray-600 dark:text-gray-400 font-mono">
                    &gt; Initialize advanced GPU performance analysis protocol
                </div>
            </div>

            <div class="mobile-card md:cyber-card md:p-8 mb-8">
                <div class="grid grid-cols-1 xl:grid-cols-3 gap-4 md:gap-8">
                    <!-- Enhanced 3D Rendering Area -->
                    <div class="xl:col-span-2">
                        <!-- Renderer Header -->
                        <div class="mb-6 flex justify-between items-center">
                            <div>
                                <h3 class="text-xl font-bold text-gray-800 dark:text-gray-200 mb-1">
                                    Volume Shader Renderer
                                </h3>
                                <div class="text-sm text-gray-500 dark:text-gray-400 font-mono">
                                    Real-time Ray Marching Engine
                                </div>
                            </div>
                            <div class="flex space-x-3">
                                <button id="fullscreen-btn" class="glass px-4 py-2 rounded-lg border border-white/20 hover:bg-white/10 transition-all duration-300 group">
                                    <i class="fas fa-expand mr-2 text-primary-400 group-hover:scale-110 transition-transform"></i>
                                    <span class="text-sm font-medium">Fullscreen</span>
                                </button>
                                <button id="screenshot-btn" class="glass px-4 py-2 rounded-lg border border-white/20 hover:bg-white/10 transition-all duration-300 group">
                                    <i class="fas fa-camera mr-2 text-secondary-400 group-hover:scale-110 transition-transform"></i>
                                    <span class="text-sm font-medium">Capture</span>
                                </button>
                            </div>
                        </div>

                        <!-- Mobile-Enhanced WebGL Canvas -->
                        <div id="canvas-container" class="mobile-canvas-container mobile-canvas-full relative bg-black md:rounded-xl overflow-hidden border-2 border-primary-400/30 neon-border md:border-primary-400/30" style="height: 50vh; min-height: 400px; md:height: 500px;">
                            <!-- Canvas Element -->
                            <canvas id="mushroom-canvas" class="w-full h-full"></canvas>

                            <!-- Mobile-Optimized Corner Decorations -->
                            <div class="absolute top-2 left-2 w-3 md:w-4 h-3 md:h-4 border-l-2 border-t-2 border-primary-400/60"></div>
                            <div class="absolute top-2 right-2 w-3 md:w-4 h-3 md:h-4 border-r-2 border-t-2 border-primary-400/60"></div>
                            <div class="absolute bottom-2 left-2 w-3 md:w-4 h-3 md:h-4 border-l-2 border-b-2 border-primary-400/60"></div>
                            <div class="absolute bottom-2 right-2 w-3 md:w-4 h-3 md:h-4 border-r-2 border-b-2 border-primary-400/60"></div>

                            <!-- Mobile Status Bar -->
                            <div class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-400 to-secondary-400 md:hidden"></div>

                            <!-- Enhanced Status Overlay -->
                            <div id="render-overlay" class="absolute inset-0 flex items-center justify-center bg-black/70 backdrop-blur-md">
                                <div class="text-center text-white max-w-md mx-auto p-6">
                                    <!-- Animated Icon -->
                                    <div class="relative mb-6">
                                        <div class="w-20 h-20 mx-auto bg-gradient-to-br from-primary-400 to-secondary-500 rounded-full flex items-center justify-center animate-pulse-slow">
                                            <i class="fas fa-cube text-3xl animate-float"></i>
                                        </div>
                                        <div class="absolute inset-0 w-20 h-20 mx-auto bg-gradient-to-br from-primary-400 to-secondary-500 rounded-full blur-lg opacity-50 animate-glow"></div>
                                    </div>

                                    <!-- Status Text -->
                                    <div class="space-y-3">
                                        <h3 class="text-xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent">
                                            VOLUME RENDERER READY
                                        </h3>
                                        <div class="hidden lg:block text-gray-300">
                                            Initialize volume shader testing protocol to begin advanced GPU analysis
                                        </div>
                                        <div class="lg:hidden text-gray-300">
                                            Tap below to start volume shader test
                                        </div>
                                        <div class="text-sm text-gray-400 font-mono">
                                            &gt; Ray marching • Fractal computation • Real-time analysis
                                        </div>
                                    </div>

                                    <!-- Loading Animation -->
                                    <div class="mt-6 flex justify-center space-x-1">
                                        <div class="w-2 h-2 bg-primary-400 rounded-full animate-bounce" style="animation-delay: 0s;"></div>
                                        <div class="w-2 h-2 bg-secondary-400 rounded-full animate-bounce" style="animation-delay: 0.1s;"></div>
                                        <div class="w-2 h-2 bg-cyber-400 rounded-full animate-bounce" style="animation-delay: 0.2s;"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Enhanced Mobile Controls Overlay -->
                            <div id="mobile-controls-overlay" class="mobile-nav-enhanced absolute inset-0 lg:hidden" style="display: none;">
                                <div class="flex flex-col h-full">
                                    <!-- Enhanced Header -->
                                    <div class="flex items-center justify-between p-4 border-b border-white/10 flex-shrink-0">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-gradient-to-br from-primary-400 to-secondary-500 rounded-lg flex items-center justify-center">
                                                <i class="fas fa-cube text-white text-sm"></i>
                                            </div>
                                            <div>
                                                <h3 class="text-white font-bold text-lg">Volume Test</h3>
                                                <div class="text-xs text-gray-400 font-mono">Select Mode</div>
                                            </div>
                                        </div>
                                        <button id="close-mobile-controls" class="w-10 h-10 rounded-full glass border border-white/20 flex items-center justify-center text-white hover:bg-white/10 transition-colors">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>

                                    <!-- Content Area -->
                                    <div class="flex-1 flex flex-col overflow-hidden">

                                        <!-- Enhanced Test Mode Selection -->
                                        <div class="flex-1 overflow-y-auto px-4 py-2">
                                            <div class="space-y-4">
                                                <div class="mobile-test-mode-card active" data-mode="light">
                                                    <div class="flex items-center space-x-4 p-4 rounded-xl glass border border-primary-400/30 hover:border-primary-400/50 transition-all duration-300">
                                                        <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-500 rounded-xl flex items-center justify-center">
                                                            <i class="fas fa-feather text-white text-lg"></i>
                                                        </div>
                                                        <div class="flex-1">
                                                            <h4 class="text-white font-bold text-lg mb-1">Basic Volume</h4>
                                                            <p class="text-gray-300 text-sm leading-relaxed">Simple ray marching with low complexity, perfect for mobile devices</p>
                                                        </div>
                                                        <div class="w-6 h-6 rounded-full border-2 border-primary-400 flex items-center justify-center">
                                                            <div class="w-3 h-3 bg-primary-400 rounded-full"></div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="mobile-test-mode-card" data-mode="medium">
                                                    <div class="flex items-center space-x-4 p-4 rounded-xl glass border border-white/20 hover:border-secondary-400/50 transition-all duration-300">
                                                        <div class="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center">
                                                            <i class="fas fa-shield-alt text-white text-lg"></i>
                                                        </div>
                                                        <div class="flex-1">
                                                            <h4 class="text-white font-bold text-lg mb-1">Standard Volume</h4>
                                                            <p class="text-gray-300 text-sm leading-relaxed">Enhanced fractal rendering with balanced complexity</p>
                                                        </div>
                                                        <div class="w-6 h-6 rounded-full border-2 border-white/30 flex items-center justify-center">
                                                            <i class="far fa-circle text-white/50"></i>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="mobile-test-mode-card" data-mode="heavy">
                                                    <div class="flex items-center space-x-4 p-4 rounded-xl glass border border-white/20 hover:border-orange-400/50 transition-all duration-300">
                                                        <div class="w-12 h-12 bg-gradient-to-br from-orange-400 to-red-500 rounded-xl flex items-center justify-center">
                                                            <i class="fas fa-fire text-white text-lg"></i>
                                                        </div>
                                                        <div class="flex-1">
                                                            <h4 class="text-white font-bold text-lg mb-1">Advanced Volume</h4>
                                                            <p class="text-gray-300 text-sm leading-relaxed">High-detail ray marching with complex shaders</p>
                                                        </div>
                                                        <div class="w-6 h-6 rounded-full border-2 border-white/30 flex items-center justify-center">
                                                            <i class="far fa-circle text-white/50"></i>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="mobile-test-mode-card" data-mode="extreme">
                                                    <div class="flex items-center space-x-4 p-4 rounded-xl glass border border-white/20 hover:border-red-400/50 transition-all duration-300">
                                                        <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-xl flex items-center justify-center">
                                                            <i class="fas fa-bomb text-white text-lg"></i>
                                                        </div>
                                                        <div class="flex-1">
                                                            <h4 class="text-white font-bold text-lg mb-1">Extreme Volume</h4>
                                                            <p class="text-gray-300 text-sm leading-relaxed">Maximum complexity fractal computation</p>
                                                        </div>
                                                        <div class="w-6 h-6 rounded-full border-2 border-white/30 flex items-center justify-center">
                                                            <i class="far fa-circle text-white/50"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                        </div>
                                    </div>

                                            <!-- Enhanced Test Control Buttons -->
                                            <div class="p-6 flex-shrink-0 space-y-4 border-t border-white/10">
                                                <button id="mobile-start-test-btn" class="w-full bg-gradient-to-r from-primary-500 to-secondary-500 text-white py-4 px-6 rounded-xl font-bold text-lg hover:from-primary-600 hover:to-secondary-600 transition-all duration-300 flex items-center justify-center shadow-2xl transform hover:scale-105">
                                                    <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mr-3">
                                                        <i class="fas fa-play text-sm"></i>
                                                    </div>
                                                    START VOLUME TEST
                                                </button>

                                                <button id="mobile-stop-test-btn" class="w-full bg-gradient-to-r from-red-500 to-pink-600 text-white py-4 px-6 rounded-xl font-bold text-lg hover:from-red-600 hover:to-pink-700 transition-all duration-300 flex items-center justify-center shadow-2xl opacity-50 transform" disabled>
                                                    <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mr-3">
                                                        <i class="fas fa-stop text-sm"></i>
                                                    </div>
                                                    STOP TEST
                                                </button>

                                                <!-- Quick Stats -->
                                                <div class="grid grid-cols-3 gap-2 mt-4">
                                                    <div class="glass rounded-lg p-2 text-center border border-white/10">
                                                        <div class="text-xs text-gray-400">FPS</div>
                                                        <div class="text-sm font-bold text-green-400">0</div>
                                                    </div>
                                                    <div class="glass rounded-lg p-2 text-center border border-white/10">
                                                        <div class="text-xs text-gray-400">Score</div>
                                                        <div class="text-sm font-bold text-blue-400">0</div>
                                                    </div>
                                                    <div class="glass rounded-lg p-2 text-center border border-white/10">
                                                        <div class="text-xs text-gray-400">Status</div>
                                                        <div class="text-sm font-bold text-yellow-400">Ready</div>
                                                    </div>
                                                </div>
                                            </div>
                                </div>
                            </div>
                        </div>



                            <!-- Advanced Performance HUD -->
                            <div id="performance-hud" class="absolute top-4 left-4 glass rounded-xl p-3 md:p-4 text-white text-xs md:text-sm font-mono border border-white/20 hidden min-w-[240px] md:min-w-[280px]">
                                <div class="flex items-center justify-between mb-3">
                                    <h4 class="text-xs font-bold text-primary-400 uppercase tracking-wider">Performance Monitor</h4>
                                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                </div>
                                <div class="grid grid-cols-2 gap-3">
                                    <div class="bg-black/30 rounded-lg p-2">
                                        <div class="text-xs text-gray-400 mb-1">Frame Rate</div>
                                        <div class="text-lg font-bold">
                                            <span id="fps-display" class="text-green-400">0</span>
                                            <span class="text-xs text-gray-400 ml-1">FPS</span>
                                        </div>
                                    </div>
                                    <div class="bg-black/30 rounded-lg p-2">
                                        <div class="text-xs text-gray-400 mb-1">Ray Steps</div>
                                        <div class="text-lg font-bold">
                                            <span id="render-time" class="text-blue-400">0</span>
                                        </div>
                                    </div>
                                    <div class="bg-black/30 rounded-lg p-2">
                                        <div class="text-xs text-gray-400 mb-1">Iterations</div>
                                        <div class="text-lg font-bold">
                                            <span id="triangle-count" class="text-yellow-400">0</span>
                                        </div>
                                    </div>
                                    <div class="bg-black/30 rounded-lg p-2">
                                        <div class="text-xs text-gray-400 mb-1">Complexity</div>
                                        <div class="text-lg font-bold">
                                            <span id="complexity-level" class="text-purple-400">1.0x</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        <!-- Warning Message -->
                        <div id="warning-overlay" class="absolute bottom-2 left-2 right-2 bg-red-600/90 backdrop-blur-sm rounded-lg p-3 md:p-2 text-white text-sm hidden">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <span id="warning-text">Performance issue detected, consider lowering test level</span>
                        </div>
                    </div>

                        <!-- Enhanced System Information -->
                        <div class="mt-6 glass rounded-xl p-6 border border-white/20">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="font-bold text-gray-800 dark:text-gray-200">System Analysis</h3>
                                <div class="flex items-center space-x-2">
                                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                    <span class="text-xs text-gray-500 dark:text-gray-400 font-mono">ONLINE</span>
                                </div>
                            </div>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-3 bg-black/10 dark:bg-white/5 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-microchip text-primary-400"></i>
                                        <span class="font-medium">GPU</span>
                                    </div>
                                    <span id="gpu-info" class="text-sm font-mono text-gray-600 dark:text-gray-400">Detecting...</span>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-black/10 dark:bg-white/5 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-cube text-secondary-400"></i>
                                        <span class="font-medium">WebGL</span>
                                    </div>
                                    <span id="webgl-version" class="text-sm font-mono text-gray-600 dark:text-gray-400">Detecting...</span>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-black/10 dark:bg-white/5 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-globe text-cyber-400"></i>
                                        <span class="font-medium">Browser</span>
                                    </div>
                                    <span id="browser-info" class="text-sm font-mono text-gray-600 dark:text-gray-400">Detecting...</span>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-black/10 dark:bg-white/5 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-desktop text-accent-400"></i>
                                        <span class="font-medium">OS</span>
                                    </div>
                                    <span id="os-info" class="text-sm font-mono text-gray-600 dark:text-gray-400">Detecting...</span>
                                </div>
                            </div>
                        </div>
                </div>

                <!-- Control Panel and Results Area -->
                <div class="space-y-4 hidden lg:block">
                    <!-- Test Mode Selection Area -->
                    <div class="test-modes-container">
                            <a href="#" class="test-mode-card active" data-mode="light">
                                <div class="mode-icon">
                                    <i class="fas fa-feather"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Basic Volume</h3>
                                    <p>Simple ray marching</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="medium">
                                <div class="mode-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Standard Volume</h3>
                                    <p>Enhanced fractal rendering</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="heavy">
                                <div class="mode-icon">
                                    <i class="fas fa-fire"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Advanced Volume</h3>
                                    <p>High-detail ray marching</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="extreme">
                                <div class="mode-icon">
                                    <i class="fas fa-bomb"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Extreme Volume</h3>
                                    <p>Maximum complexity</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="custom">
                                <div class="mode-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Custom</h3>
                                    <p>Custom parameters</p>
                                </div>
                            </a>
                        </div>

                    <!-- Test Controls -->
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">Volume Test Controls</h3>

                        <!-- Test Buttons -->
                        <div class="grid grid-cols-2 gap-2 mb-4">
                            <button id="start-test-btn" class="btn-primary flex items-center justify-center h-10">
                                <i class="fas fa-play mr-2"></i> Start Test
                            </button>
                            <button id="stop-test-btn" class="btn-danger flex items-center justify-center h-10 opacity-50" disabled>
                                <i class="fas fa-stop mr-2"></i> Stop Test
                            </button>
                        </div>

                        <!-- Test Progress -->
                        <div id="test-progress" class="hidden mb-4">
                            <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                <span>Test Progress</span>
                                <span id="progress-text">0/60 seconds</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div id="progress-bar" class="bg-gradient-to-r from-primary-500 to-secondary-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                        </div>

                        <!-- Custom Parameters -->
                        <div id="custom-params" class="space-y-3 hidden">
                            <div>
                                <label for="triangle-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Ray Steps</label>
                                <input type="range" id="triangle-slider" min="100" max="2000" value="800" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Current: <span id="triangle-value">800</span></div>
                            </div>
                            <div>
                                <label for="complexity-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Fractal Iterations</label>
                                <input type="range" id="complexity-slider" min="2" max="16" step="1" value="8" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Current: <span id="complexity-value">8</span></div>
                            </div>
                            <div>
                                <label for="duration-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Test Duration (seconds)</label>
                                <input type="range" id="duration-slider" min="10" max="300" value="60" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Current: <span id="duration-value">60</span> seconds</div>
                            </div>
                        </div>
                    </div>

                    <!-- Current Performance Metrics -->
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">Real-time Performance</h3>
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600 dark:text-green-400" id="current-fps">0</div>
                                <div class="text-gray-500 dark:text-gray-400">FPS</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400" id="current-score">0</div>
                                <div class="text-gray-500 dark:text-gray-400">Score</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-yellow-600 dark:text-yellow-400" id="current-temp">Normal</div>
                                <div class="text-gray-500 dark:text-gray-400">Temperature Status</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-purple-600 dark:text-purple-400" id="current-level">Basic Volume</div>
                                <div class="text-gray-500 dark:text-gray-400">Test Level</div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="grid grid-cols-2 gap-2">
                        <button id="share-btn" class="btn-secondary flex items-center justify-center h-10">
                            <i class="fas fa-share-alt mr-2"></i> Share Results
                        </button>
                        <button id="history-btn" class="btn-secondary flex items-center justify-center h-10">
                            <i class="fas fa-history mr-2"></i> Test History
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Control Panel Card -->
        <div class="md:hidden mobile-card mt-8">
            <!-- Volume Test Mode Selection -->
            <div class="mb-6">
                <h3 class="text-lg font-bold text-gray-800 dark:text-gray-200 mb-4 flex items-center">
                    <div class="w-8 h-8 bg-gradient-to-br from-primary-400 to-secondary-500 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-cube text-white text-sm"></i>
                    </div>
                    Volume Test Modes
                </h3>

                <div class="space-y-3">
                    <!-- Basic Volume -->
                    <div class="mobile-test-mode-card active" data-mode="light">
                        <div class="flex items-center space-x-4 p-4 rounded-xl glass border border-primary-400/30 hover:border-primary-400/50 transition-all duration-300">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-500 rounded-lg flex items-center justify-center">
                                <i class="fas fa-feather text-white"></i>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-gray-800 dark:text-gray-200 font-bold mb-1">Basic Volume</h4>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">Simple ray marching</p>
                            </div>
                            <div class="w-6 h-6 rounded-full border-2 border-primary-400 flex items-center justify-center">
                                <div class="w-3 h-3 bg-primary-400 rounded-full"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Standard Volume -->
                    <div class="mobile-test-mode-card" data-mode="medium">
                        <div class="flex items-center space-x-4 p-4 rounded-xl glass border border-white/20 hover:border-secondary-400/50 transition-all duration-300">
                            <div class="w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center">
                                <i class="fas fa-shield-alt text-white"></i>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-gray-800 dark:text-gray-200 font-bold mb-1">Standard Volume</h4>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">Enhanced fractal rendering</p>
                            </div>
                            <div class="w-6 h-6 rounded-full border-2 border-white/30 flex items-center justify-center">
                                <i class="far fa-circle text-gray-400"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Volume -->
                    <div class="mobile-test-mode-card" data-mode="heavy">
                        <div class="flex items-center space-x-4 p-4 rounded-xl glass border border-white/20 hover:border-orange-400/50 transition-all duration-300">
                            <div class="w-10 h-10 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg flex items-center justify-center">
                                <i class="fas fa-fire text-white"></i>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-gray-800 dark:text-gray-200 font-bold mb-1">Advanced Volume</h4>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">High-detail ray marching</p>
                            </div>
                            <div class="w-6 h-6 rounded-full border-2 border-white/30 flex items-center justify-center">
                                <i class="far fa-circle text-gray-400"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Extreme Volume -->
                    <div class="mobile-test-mode-card" data-mode="extreme">
                        <div class="flex items-center space-x-4 p-4 rounded-xl glass border border-white/20 hover:border-red-400/50 transition-all duration-300">
                            <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-pink-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-bomb text-white"></i>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-gray-800 dark:text-gray-200 font-bold mb-1">Extreme Volume</h4>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">Maximum complexity</p>
                            </div>
                            <div class="w-6 h-6 rounded-full border-2 border-white/30 flex items-center justify-center">
                                <i class="far fa-circle text-gray-400"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Custom -->
                    <div class="mobile-test-mode-card" data-mode="custom">
                        <div class="flex items-center space-x-4 p-4 rounded-xl glass border border-white/20 hover:border-purple-400/50 transition-all duration-300">
                            <div class="w-10 h-10 bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-cog text-white"></i>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-gray-800 dark:text-gray-200 font-bold mb-1">Custom</h4>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">Custom parameters</p>
                            </div>
                            <div class="w-6 h-6 rounded-full border-2 border-white/30 flex items-center justify-center">
                                <i class="far fa-circle text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Volume Test Controls -->
            <div class="mb-6">
                <h3 class="text-lg font-bold text-gray-800 dark:text-gray-200 mb-4">Volume Test Controls</h3>

                <div class="grid grid-cols-2 gap-3 mb-4">
                    <button id="mobile-start-test-btn" class="bg-gradient-to-r from-primary-500 to-secondary-500 text-white py-4 px-4 rounded-xl font-bold hover:from-primary-600 hover:to-secondary-600 transition-all duration-300 flex items-center justify-center shadow-lg">
                        <i class="fas fa-play mr-2"></i>
                        Start Test
                    </button>
                    <button id="mobile-stop-test-btn" class="bg-gradient-to-r from-red-500 to-pink-600 text-white py-4 px-4 rounded-xl font-bold hover:from-red-600 hover:to-pink-700 transition-all duration-300 flex items-center justify-center shadow-lg opacity-50" disabled>
                        <i class="fas fa-stop mr-2"></i>
                        Stop Test
                    </button>
                </div>

                <!-- Test Progress -->
                <div id="mobile-test-progress" class="hidden mb-4">
                    <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
                        <span>Test Progress</span>
                        <span id="mobile-progress-text">0/60 seconds</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div id="mobile-progress-bar" class="bg-gradient-to-r from-primary-500 to-secondary-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                </div>
            </div>

            <!-- Real-time Performance -->
            <div class="mb-6">
                <h3 class="text-lg font-bold text-gray-800 dark:text-gray-200 mb-4">Real-time Performance</h3>

                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div class="text-center glass rounded-lg p-4 border border-white/20">
                        <div class="text-3xl font-bold text-green-600 dark:text-green-400" id="mobile-current-fps">0</div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">FPS</div>
                    </div>
                    <div class="text-center glass rounded-lg p-4 border border-white/20">
                        <div class="text-3xl font-bold text-blue-600 dark:text-blue-400" id="mobile-current-score">0</div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">Score</div>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center glass rounded-lg p-4 border border-white/20">
                        <div class="text-lg font-medium text-yellow-600 dark:text-yellow-400" id="mobile-current-temp">Normal</div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">Temperature Status</div>
                    </div>
                    <div class="text-center glass rounded-lg p-4 border border-white/20">
                        <div class="text-lg font-medium text-purple-600 dark:text-purple-400" id="mobile-current-level">Basic Volume</div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">Test Level</div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div>
                <h3 class="text-lg font-bold text-gray-800 dark:text-gray-200 mb-4">Quick Actions</h3>

                <div class="grid grid-cols-2 gap-3">
                    <button id="mobile-share-btn" class="glass border border-white/20 py-3 px-4 rounded-xl font-medium hover:bg-white/10 transition-all duration-300 flex items-center justify-center">
                        <i class="fas fa-share-alt mr-2 text-primary-400"></i>
                        Share Results
                    </button>
                    <button id="mobile-history-btn" class="glass border border-white/20 py-3 px-4 rounded-xl font-medium hover:bg-white/10 transition-all duration-300 flex items-center justify-center">
                        <i class="fas fa-history mr-2 text-secondary-400"></i>
                        Test History
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile-Enhanced Performance Charts -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-8 mobile-section-spacing md:mt-16 px-4 md:px-0">
            <!-- Performance Chart Area -->
            <div class="mobile-card md:cyber-card md:p-8 mb-0 flex flex-col hover-lift" style="height: 350px; md:height: 450px;">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Volume Performance Analysis</h2>
                    <div class="flex space-x-2">
                        <button class="chart-tab active" data-chart="fps">FPS Chart</button>
                        <button class="chart-tab" data-chart="score">Score Trend</button>
                    </div>
                </div>
                <div class="chart-content flex-grow">
                    <div class="chart-wrapper h-full" id="fps-chart-wrapper" style="display:block;">
                        <canvas id="fpsChart" class="w-full h-full"></canvas>
                    </div>
                    <div class="chart-wrapper h-full" id="score-chart-wrapper" style="display:none;">
                        <canvas id="scoreChart" class="w-full h-full"></canvas>
                    </div>
                </div>
                <div class="mt-4 grid grid-cols-2 sm:grid-cols-4 gap-2 text-center">
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">Average FPS</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="avg-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">Maximum FPS</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="max-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">Minimum FPS</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="min-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">Stability</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="stability">-</div>
                    </div>
                </div>
            </div>

            <!-- Test History Area -->
            <div class="card p-6 mb-0 flex flex-col" style="height: 400px;">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Volume Test History</h2>
                    <button class="clear-history-btn btn-danger text-sm py-1 px-3">
                        <i class="fas fa-trash-alt mr-1"></i> Clear
                    </button>
                </div>
                <div class="history-log flex-grow overflow-y-auto bg-white/50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="empty-log p-4 text-center text-gray-500 dark:text-gray-400 flex flex-col items-center justify-center h-full">
                        <i class="fas fa-chart-line text-4xl mb-2 opacity-30"></i>
                        <p>No volume test records yet</p>
                        <p class="text-sm">Completed volume shader tests will appear here</p>
                    </div>
                    <div id="history-items" class="hidden max-h-full overflow-y-auto">
                        <!-- History items will be dynamically added via JavaScript -->
                    </div>
                </div>
                <div class="mt-4 flex justify-between">
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        Total <span id="history-count">0</span> tests
                    </div>
                    <div class="flex space-x-2">
                        <button id="export-history" class="btn-primary text-sm py-1 px-3 flex items-center">
                            <i class="fas fa-download mr-1"></i> Export
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile-Enhanced Technical Details -->
        <div id="technical-details" class="mobile-card md:cyber-card md:p-10 mobile-section-spacing md:mt-20 hover-lift">
            <div class="text-center mb-6 md:mb-10 px-4 md:px-0">
                <h2 class="mobile-section-title md:text-4xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent mb-2 md:mb-4">
                    Technical Implementation Details
                </h2>
                <div class="w-24 md:w-32 h-1 bg-gradient-to-r from-primary-400 to-secondary-400 mx-auto rounded-full mb-2"></div>
                <div class="mobile-section-subtitle md:text-sm text-gray-600 dark:text-gray-400 font-mono">
                    &gt; Deep dive into volume shader architecture and algorithms
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-xl font-semibold mb-4 text-primary-600 dark:text-primary-400">Volume Rendering Technology</h3>
                    <div class="space-y-4 text-gray-700 dark:text-gray-300">
                        <p>
                            Our volume shader test utilizes advanced <strong>ray marching algorithms</strong> to render complex
                            three-dimensional fractal structures in real-time. This technique involves casting rays from the
                            camera through each pixel and sampling the volume at regular intervals to determine color and opacity.
                        </p>
                        <p>
                            The core rendering pipeline implements a sophisticated <strong>distance field evaluation system</strong>
                            that calculates the signed distance to the fractal surface at any point in 3D space. This allows for
                            precise surface detection and enables high-quality lighting calculations.
                        </p>
                        <p>
                            Our fractal kernel uses an <strong>iterative escape-time algorithm</strong> with configurable complexity
                            levels, allowing the test to scale from simple geometric forms to highly detailed mathematical structures
                            that stress-test modern GPU architectures.
                        </p>
                    </div>
                </div>

                <div>
                    <h3 class="text-xl font-semibold mb-4 text-primary-600 dark:text-primary-400">Shader Architecture</h3>
                    <div class="space-y-4 text-gray-700 dark:text-gray-300">
                        <p>
                            The volume shader is implemented using <strong>WebGL 2.0 fragment shaders</strong> with high-precision
                            floating-point calculations. The shader code is optimized for parallel execution across GPU cores,
                            maximizing throughput and testing the graphics card's computational capabilities.
                        </p>
                        <p>
                            Key shader features include <strong>adaptive step sizing</strong> for ray marching optimization,
                            <strong>normal vector calculation</strong> using finite differences for realistic lighting, and
                            <strong>reflection mapping</strong> for enhanced visual quality and increased computational load.
                        </p>
                        <p>
                            The test dynamically adjusts shader complexity through uniform variables, allowing real-time
                            modification of fractal iteration counts, ray step sizes, and mathematical precision levels
                            to provide comprehensive GPU performance analysis.
                        </p>
                    </div>
                </div>
            </div>

            <div class="mt-8 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-6">
                <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Performance Metrics Explained</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-medium text-primary-600 dark:text-primary-400 mb-2">Frame Rate (FPS)</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            Measures the number of complete frames rendered per second. Higher FPS indicates better GPU performance
                            and smoother visual experience. Our test monitors FPS continuously to detect performance degradation.
                        </p>
                    </div>
                    <div>
                        <h4 class="font-medium text-primary-600 dark:text-primary-400 mb-2">Ray Marching Steps</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            The number of samples taken along each ray to determine surface intersection. More steps provide
                            higher accuracy but require more computational power, making this a key performance indicator.
                        </p>
                    </div>
                    <div>
                        <h4 class="font-medium text-primary-600 dark:text-primary-400 mb-2">Fractal Iterations</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            The number of mathematical iterations performed to generate the fractal structure. Higher iteration
                            counts produce more detailed fractals but significantly increase computational complexity.
                        </p>
                    </div>
                    <div>
                        <h4 class="font-medium text-primary-600 dark:text-primary-400 mb-2">Stability Score</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            Measures frame rate consistency over time. A high stability score indicates reliable performance
                            without significant frame drops, which is crucial for smooth real-time applications.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Guide Section -->
        <div id="performance-guide" class="cyber-card p-10 mt-20 hover-lift">
            <div class="text-center mb-10">
                <h2 class="text-4xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent mb-4">
                    Volume Shader Performance Guide
                </h2>
                <div class="w-32 h-1 bg-gradient-to-r from-primary-400 to-secondary-400 mx-auto rounded-full mb-2"></div>
                <div class="text-gray-600 dark:text-gray-400 font-mono text-sm">
                    &gt; Optimize your GPU testing experience with professional guidance
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div class="lg:col-span-2">
                    <h3 class="text-xl font-semibold mb-4 text-primary-600 dark:text-primary-400">Optimizing Your Volume Shader Experience</h3>

                    <div class="space-y-6">
                        <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                            <h4 class="font-semibold text-lg mb-3 text-gray-800 dark:text-gray-200">Pre-Test Preparation</h4>
                            <ul class="space-y-2 text-gray-700 dark:text-gray-300">
                                <li class="flex items-start space-x-2">
                                    <i class="fas fa-check-circle text-green-500 mt-1"></i>
                                    <span>Close unnecessary applications and browser tabs to free up system resources</span>
                                </li>
                                <li class="flex items-start space-x-2">
                                    <i class="fas fa-check-circle text-green-500 mt-1"></i>
                                    <span>Ensure your device has adequate cooling and ventilation</span>
                                </li>
                                <li class="flex items-start space-x-2">
                                    <i class="fas fa-check-circle text-green-500 mt-1"></i>
                                    <span>Update your graphics drivers to the latest version</span>
                                </li>
                                <li class="flex items-start space-x-2">
                                    <i class="fas fa-check-circle text-green-500 mt-1"></i>
                                    <span>Enable hardware acceleration in your browser settings</span>
                                </li>
                            </ul>
                        </div>

                        <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                            <h4 class="font-semibold text-lg mb-3 text-gray-800 dark:text-gray-200">Test Mode Selection Guidelines</h4>
                            <div class="space-y-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                        <i class="fas fa-feather text-blue-600 dark:text-blue-400 text-sm"></i>
                                    </div>
                                    <div>
                                        <strong class="text-gray-800 dark:text-gray-200">Basic Volume:</strong>
                                        <span class="text-gray-600 dark:text-gray-400">Ideal for integrated graphics, mobile devices, or initial performance assessment</span>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center">
                                        <i class="fas fa-shield-alt text-yellow-600 dark:text-yellow-400 text-sm"></i>
                                    </div>
                                    <div>
                                        <strong class="text-gray-800 dark:text-gray-200">Standard Volume:</strong>
                                        <span class="text-gray-600 dark:text-gray-400">Suitable for mid-range dedicated graphics cards and comprehensive testing</span>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center">
                                        <i class="fas fa-fire text-orange-600 dark:text-orange-400 text-sm"></i>
                                    </div>
                                    <div>
                                        <strong class="text-gray-800 dark:text-gray-200">Advanced Volume:</strong>
                                        <span class="text-gray-600 dark:text-gray-400">Designed for high-end graphics cards and professional workstations</span>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                                        <i class="fas fa-bomb text-red-600 dark:text-red-400 text-sm"></i>
                                    </div>
                                    <div>
                                        <strong class="text-gray-800 dark:text-gray-200">Extreme Volume:</strong>
                                        <span class="text-gray-600 dark:text-gray-400">Maximum stress test for flagship GPUs and thermal limit testing</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                            <h4 class="font-semibold text-lg mb-3 text-gray-800 dark:text-gray-200">Interpreting Test Results</h4>
                            <div class="space-y-3 text-gray-700 dark:text-gray-300">
                                <p>
                                    <strong>Excellent Performance (60+ FPS):</strong> Your GPU handles volume shaders exceptionally well.
                                    Consider trying higher complexity levels or using this configuration for demanding 3D applications.
                                </p>
                                <p>
                                    <strong>Good Performance (30-60 FPS):</strong> Solid performance suitable for most volume rendering tasks.
                                    This indicates a well-balanced system capable of handling moderate computational loads.
                                </p>
                                <p>
                                    <strong>Moderate Performance (15-30 FPS):</strong> Acceptable for basic volume rendering but may struggle
                                    with complex scenes. Consider optimizing settings or upgrading hardware for better performance.
                                </p>
                                <p>
                                    <strong>Poor Performance (&lt;15 FPS):</strong> Indicates hardware limitations or system bottlenecks.
                                    Try lower complexity settings or check for driver updates and system optimization opportunities.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 class="text-xl font-semibold mb-4 text-primary-600 dark:text-primary-400">Performance Tips</h3>
                    <div class="space-y-4">
                        <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800">
                            <h4 class="font-medium text-green-800 dark:text-green-200 mb-2">
                                <i class="fas fa-lightbulb mr-2"></i>Browser Optimization
                            </h4>
                            <ul class="text-sm text-green-700 dark:text-green-300 space-y-1">
                                <li>• Use Chrome or Firefox for best WebGL support</li>
                                <li>• Enable hardware acceleration</li>
                                <li>• Close other GPU-intensive tabs</li>
                                <li>• Disable browser extensions temporarily</li>
                            </ul>
                        </div>

                        <div class="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                            <h4 class="font-medium text-blue-800 dark:text-blue-200 mb-2">
                                <i class="fas fa-cogs mr-2"></i>System Settings
                            </h4>
                            <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                                <li>• Set power plan to "High Performance"</li>
                                <li>• Ensure adequate system cooling</li>
                                <li>• Close background applications</li>
                                <li>• Monitor system temperatures</li>
                            </ul>
                        </div>

                        <div class="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-800">
                            <h4 class="font-medium text-purple-800 dark:text-purple-200 mb-2">
                                <i class="fas fa-chart-line mr-2"></i>Monitoring Tools
                            </h4>
                            <ul class="text-sm text-purple-700 dark:text-purple-300 space-y-1">
                                <li>• Watch real-time FPS counter</li>
                                <li>• Monitor temperature warnings</li>
                                <li>• Check stability percentage</li>
                                <li>• Review performance history</li>
                            </ul>
                        </div>

                        <div class="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-lg p-4 border border-orange-200 dark:border-orange-800">
                            <h4 class="font-medium text-orange-800 dark:text-orange-200 mb-2">
                                <i class="fas fa-exclamation-triangle mr-2"></i>Safety Precautions
                            </h4>
                            <ul class="text-sm text-orange-700 dark:text-orange-300 space-y-1">
                                <li>• Stop test if temperatures exceed safe limits</li>
                                <li>• Don't run extreme tests on older hardware</li>
                                <li>• Allow cooling time between tests</li>
                                <li>• Monitor for system instability</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Shader Analysis Section -->
        <div id="shader-analysis" class="cyber-card p-10 mt-20 hover-lift">
            <div class="text-center mb-10">
                <h2 class="text-4xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent mb-4">
                    Advanced Shader Analysis
                </h2>
                <div class="w-32 h-1 bg-gradient-to-r from-primary-400 to-secondary-400 mx-auto rounded-full mb-2"></div>
                <div class="text-gray-600 dark:text-gray-400 font-mono text-sm">
                    &gt; Comprehensive analysis of ray marching algorithms and fractal mathematics
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-xl font-semibold mb-4 text-primary-600 dark:text-primary-400">Ray Marching Algorithm</h3>
                    <div class="bg-gray-900 rounded-lg p-4 mb-4 overflow-x-auto">
                        <pre class="text-green-400 text-sm"><code>// Core ray marching loop
for(int k = 2; k < max_steps; k++) {
    vec3 pos = origin + dir * (step_size * float(k));
    float distance = fractalKernel(pos);

    if(distance > 0.0 && prev_distance < 0.0) {
        // Surface intersection detected
        vec3 surface_point = refineSurfacePosition(pos);
        vec3 normal = calculateNormal(surface_point);
        color = computeLighting(surface_point, normal);
        break;
    }
    prev_distance = distance;
}</code></pre>
                    </div>
                    <p class="text-gray-700 dark:text-gray-300 mb-4">
                        The ray marching algorithm steps through 3D space along viewing rays, sampling the fractal
                        distance field at regular intervals. When a sign change is detected, indicating surface
                        intersection, the algorithm refines the position using binary search for sub-pixel accuracy.
                    </p>

                    <h4 class="font-semibold text-lg mb-2 text-gray-800 dark:text-gray-200">Key Algorithm Features:</h4>
                    <ul class="space-y-2 text-gray-700 dark:text-gray-300">
                        <li class="flex items-start space-x-2">
                            <i class="fas fa-arrow-right text-primary-500 mt-1"></i>
                            <span><strong>Adaptive Step Sizing:</strong> Dynamically adjusts step size based on complexity level</span>
                        </li>
                        <li class="flex items-start space-x-2">
                            <i class="fas fa-arrow-right text-primary-500 mt-1"></i>
                            <span><strong>Binary Search Refinement:</strong> Achieves sub-pixel surface accuracy</span>
                        </li>
                        <li class="flex items-start space-x-2">
                            <i class="fas fa-arrow-right text-primary-500 mt-1"></i>
                            <span><strong>Early Termination:</strong> Optimizes performance by stopping unnecessary calculations</span>
                        </li>
                        <li class="flex items-start space-x-2">
                            <i class="fas fa-arrow-right text-primary-500 mt-1"></i>
                            <span><strong>Golden Section Search:</strong> Efficiently finds optimal surface points</span>
                        </li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-xl font-semibold mb-4 text-primary-600 dark:text-primary-400">Fractal Mathematics</h3>
                    <div class="bg-gray-900 rounded-lg p-4 mb-4 overflow-x-auto">
                        <pre class="text-blue-400 text-sm"><code>// Fractal kernel computation
float fractalKernel(vec3 pos) {
    vec3 z = pos;
    float r, theta, phi;

    for(int i = 0; i < iterations; i++) {
        r = length(z);
        if(r > escape_radius) break;

        theta = atan(z.y, z.x) * power;
        phi = acos(clamp(z.z / r, -1.0, 1.0)) * power;
        r = pow(r, power);

        z = vec3(r * sin(phi) * cos(theta),
                 r * sin(phi) * sin(theta),
                 r * cos(phi)) + pos;
    }

    return escape_radius - length(z);
}</code></pre>
                    </div>
                    <p class="text-gray-700 dark:text-gray-300 mb-4">
                        The fractal kernel implements a 3D extension of the Mandelbrot set using spherical coordinates.
                        Each iteration transforms the current point through a power function, creating complex self-similar
                        structures that challenge GPU computational capabilities.
                    </p>

                    <h4 class="font-semibold text-lg mb-2 text-gray-800 dark:text-gray-200">Mathematical Properties:</h4>
                    <ul class="space-y-2 text-gray-700 dark:text-gray-300">
                        <li class="flex items-start space-x-2">
                            <i class="fas fa-infinity text-secondary-500 mt-1"></i>
                            <span><strong>Self-Similarity:</strong> Fractal patterns repeat at different scales</span>
                        </li>
                        <li class="flex items-start space-x-2">
                            <i class="fas fa-infinity text-secondary-500 mt-1"></i>
                            <span><strong>Escape-Time Algorithm:</strong> Determines fractal membership through iteration</span>
                        </li>
                        <li class="flex items-start space-x-2">
                            <i class="fas fa-infinity text-secondary-500 mt-1"></i>
                            <span><strong>Power Scaling:</strong> Configurable exponent affects fractal complexity</span>
                        </li>
                        <li class="flex items-start space-x-2">
                            <i class="fas fa-infinity text-secondary-500 mt-1"></i>
                            <span><strong>Spherical Mapping:</strong> 3D coordinate transformation for volume rendering</span>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="mt-8 bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg p-6">
                <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Performance Impact Analysis</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-microchip text-red-600 dark:text-red-400 text-2xl"></i>
                        </div>
                        <h4 class="font-semibold text-gray-800 dark:text-gray-200 mb-2">Computational Intensity</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            Each pixel requires hundreds of mathematical operations, including trigonometric functions,
                            power calculations, and vector operations, creating significant GPU load.
                        </p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-memory text-yellow-600 dark:text-yellow-400 text-2xl"></i>
                        </div>
                        <h4 class="font-semibold text-gray-800 dark:text-gray-200 mb-2">Memory Bandwidth</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            High-precision floating-point calculations and frequent memory access patterns test
                            the GPU's memory subsystem and cache efficiency.
                        </p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-thermometer-half text-green-600 dark:text-green-400 text-2xl"></i>
                        </div>
                        <h4 class="font-semibold text-gray-800 dark:text-gray-200 mb-2">Thermal Characteristics</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            Sustained high computational load generates significant heat, making this test ideal
                            for thermal throttling detection and cooling system evaluation.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div id="faq" class="cyber-card p-10 mt-20 hover-lift">
            <div class="text-center mb-10">
                <h2 class="text-4xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent mb-4">
                    Frequently Asked Questions
                </h2>
                <div class="w-32 h-1 bg-gradient-to-r from-primary-400 to-secondary-400 mx-auto rounded-full mb-2"></div>
                <div class="text-gray-600 dark:text-gray-400 font-mono text-sm">
                    &gt; Common questions and expert answers about volume shader testing
                </div>
            </div>

            <div class="space-y-4 md:space-y-6 px-4 md:px-0">
                <div class="glass rounded-xl p-4 md:p-6 border border-white/20 hover-lift">
                    <h3 class="text-base md:text-lg font-bold mb-3 text-gray-800 dark:text-gray-200 flex items-start">
                        <div class="w-8 h-8 bg-gradient-to-br from-primary-400 to-secondary-500 rounded-lg flex items-center justify-center mr-3 flex-shrink-0 mt-0.5">
                            <i class="fas fa-question text-white text-sm"></i>
                        </div>
                        <span>What makes volume shader testing different from traditional GPU benchmarks?</span>
                    </h3>
                    <p class="text-sm md:text-base text-gray-700 dark:text-gray-300 leading-relaxed ml-11">
                        Volume shader testing focuses specifically on ray marching and fractal computation capabilities,
                        which are increasingly important for modern 3D applications, scientific visualization, and
                        real-time rendering. Unlike traditional polygon-based benchmarks, volume shaders test the GPU's
                        ability to handle complex mathematical operations and high-precision floating-point calculations
                        in parallel across thousands of cores.
                    </p>
                </div>

                <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">
                        <i class="fas fa-question-circle text-primary-500 mr-2"></i>
                        Why does my device get hot during volume shader testing?
                    </h3>
                    <p class="text-gray-700 dark:text-gray-300">
                        Volume shader rendering is computationally intensive, requiring the GPU to perform complex
                        mathematical calculations for every pixel on the screen. This sustained high workload causes
                        the GPU to operate at maximum capacity, generating significant heat. This is normal behavior
                        and actually helps evaluate your system's thermal management capabilities. If temperatures
                        become excessive, the test will display warnings and you should consider stopping the test
                        or reducing the complexity level.
                    </p>
                </div>

                <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">
                        <i class="fas fa-question-circle text-primary-500 mr-2"></i>
                        How do I interpret my volume shader test scores?
                    </h3>
                    <p class="text-gray-700 dark:text-gray-300 mb-3">
                        Test scores are calculated based on average frame rate, test complexity, and performance
                        stability. Here's how to interpret your results:
                    </p>
                    <ul class="space-y-2 text-gray-700 dark:text-gray-300 ml-4">
                        <li><strong>Score 1000+:</strong> Excellent performance, suitable for professional 3D work and high-end gaming</li>
                        <li><strong>Score 500-1000:</strong> Good performance, handles most volume rendering tasks well</li>
                        <li><strong>Score 200-500:</strong> Moderate performance, suitable for basic 3D applications</li>
                        <li><strong>Score &lt;200:</strong> Limited performance, may struggle with complex volume rendering</li>
                    </ul>
                </div>

                <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">
                        <i class="fas fa-question-circle text-primary-500 mr-2"></i>
                        What should I do if the test shows "WebGL not supported" error?
                    </h3>
                    <p class="text-gray-700 dark:text-gray-300">
                        This error indicates that your browser or system doesn't support WebGL, which is required
                        for volume shader testing. Try the following solutions: 1) Update your browser to the latest
                        version, 2) Enable hardware acceleration in browser settings, 3) Update your graphics drivers,
                        4) Try a different browser (Chrome and Firefox have the best WebGL support), 5) Check if your
                        graphics card supports OpenGL 3.0 or higher.
                    </p>
                </div>

                <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">
                        <i class="fas fa-question-circle text-primary-500 mr-2"></i>
                        Can I use custom parameters to test specific scenarios?
                    </h3>
                    <p class="text-gray-700 dark:text-gray-300">
                        Yes! Select the "Custom" test mode to access advanced parameters including ray marching steps,
                        fractal iteration count, and test duration. This allows you to create specific test scenarios
                        tailored to your needs, whether you're evaluating performance for a particular application
                        or conducting detailed GPU analysis. Custom parameters are particularly useful for developers
                        working with volume rendering applications.
                    </p>
                </div>

                <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">
                        <i class="fas fa-question-circle text-primary-500 mr-2"></i>
                        Is my test data stored or transmitted anywhere?
                    </h3>
                    <p class="text-gray-700 dark:text-gray-300">
                        All volume shader test data is processed and stored locally in your browser. No performance
                        data, system information, or test results are transmitted to external servers. Your privacy
                        is completely protected, and you can safely conduct tests without concerns about data collection.
                        Test history is saved in your browser's local storage and can be cleared at any time using
                        the "Clear History" button.
                    </p>
                </div>

                <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">
                        <i class="fas fa-question-circle text-primary-500 mr-2"></i>
                        How often should I run volume shader tests?
                    </h3>
                    <p class="text-gray-700 dark:text-gray-300">
                        For general users, running tests monthly or after system changes (driver updates, hardware
                        modifications) is sufficient. Professionals working with 3D applications might benefit from
                        weekly testing to monitor performance trends. Avoid running extreme tests repeatedly in short
                        periods, as this can cause unnecessary thermal stress. Always allow adequate cooling time
                        between intensive test sessions.
                    </p>
                </div>
            </div>
        </div>

        <!-- Enhanced Footer -->
        <footer class="mt-24 py-12 glass border-t border-white/20">
            <div class="text-center">
                <!-- Logo Section -->
                <div class="flex justify-center items-center space-x-3 mb-6">
                    <div class="w-8 h-8 bg-gradient-to-br from-primary-400 to-secondary-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-cube text-white text-sm"></i>
                    </div>
                    <span class="text-xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent">
                        Volume Shader Test
                    </span>
                </div>

                <!-- Description -->
                <p class="text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto font-light">
                    Advanced GPU performance analysis through sophisticated volume shader rendering and real-time fractal computation
                </p>

                <!-- Navigation Links -->
                <div class="flex flex-wrap justify-center gap-8 text-sm mb-8">
                    <a href="index.html" class="text-gray-500 dark:text-gray-400 hover:text-primary-400 transition-colors duration-300 flex items-center space-x-2">
                        <i class="fas fa-home"></i>
                        <span>Home</span>
                    </a>
                    <a href="#technical-details" class="text-gray-500 dark:text-gray-400 hover:text-primary-400 transition-colors duration-300 flex items-center space-x-2">
                        <i class="fas fa-cogs"></i>
                        <span>Technical</span>
                    </a>
                    <a href="#performance-guide" class="text-gray-500 dark:text-gray-400 hover:text-primary-400 transition-colors duration-300 flex items-center space-x-2">
                        <i class="fas fa-chart-line"></i>
                        <span>Guide</span>
                    </a>
                    <a href="#faq" class="text-gray-500 dark:text-gray-400 hover:text-primary-400 transition-colors duration-300 flex items-center space-x-2">
                        <i class="fas fa-question-circle"></i>
                        <span>FAQ</span>
                    </a>
                </div>

                <!-- Copyright -->
                <div class="pt-6 border-t border-white/10">
                    <div class="text-xs text-gray-400 dark:text-gray-500 font-mono">
                        © 2024 Toxic Mushroom Test Team • Advanced Volume Shader Testing Platform
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- JavaScript -->
    <script src="static/js/app.js"></script>

    <!-- Mobile Control Panel JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile test mode selection
            const mobileTestModeCards = document.querySelectorAll('.mobile-test-mode-card');

            mobileTestModeCards.forEach(card => {
                card.addEventListener('click', function() {
                    // Remove active class from all cards
                    mobileTestModeCards.forEach(c => {
                        c.classList.remove('active');
                        const radioIcon = c.querySelector('.w-6.h-6 div, .w-6.h-6 i');
                        if (radioIcon) {
                            if (radioIcon.classList.contains('bg-primary-400')) {
                                radioIcon.classList.remove('bg-primary-400');
                                radioIcon.classList.add('far', 'fa-circle', 'text-gray-400');
                                radioIcon.classList.remove('w-3', 'h-3', 'rounded-full');
                            } else {
                                radioIcon.className = 'far fa-circle text-gray-400';
                            }
                        }
                    });

                    // Add active class to clicked card
                    this.classList.add('active');
                    const selectedRadio = this.querySelector('.w-6.h-6');
                    if (selectedRadio) {
                        selectedRadio.innerHTML = '<div class="w-3 h-3 bg-primary-400 rounded-full"></div>';
                    }

                    // Update current level display
                    const levelText = this.querySelector('h4').textContent;
                    const mobileCurrentLevel = document.getElementById('mobile-current-level');
                    if (mobileCurrentLevel) {
                        mobileCurrentLevel.textContent = levelText;
                    }
                });
            });

            // Mobile start test button
            const mobileStartBtn = document.getElementById('mobile-start-test-btn');
            const mobileStopBtn = document.getElementById('mobile-stop-test-btn');
            const mobileProgress = document.getElementById('mobile-test-progress');

            if (mobileStartBtn) {
                mobileStartBtn.addEventListener('click', function() {
                    // Show progress
                    if (mobileProgress) {
                        mobileProgress.classList.remove('hidden');
                    }

                    // Update button states
                    this.disabled = true;
                    this.classList.add('opacity-50');
                    if (mobileStopBtn) {
                        mobileStopBtn.disabled = false;
                        mobileStopBtn.classList.remove('opacity-50');
                    }

                    // Trigger the main test start function
                    if (window.startTest) {
                        window.startTest();
                    }
                });
            }

            if (mobileStopBtn) {
                mobileStopBtn.addEventListener('click', function() {
                    // Hide progress
                    if (mobileProgress) {
                        mobileProgress.classList.add('hidden');
                    }

                    // Update button states
                    this.disabled = true;
                    this.classList.add('opacity-50');
                    if (mobileStartBtn) {
                        mobileStartBtn.disabled = false;
                        mobileStartBtn.classList.remove('opacity-50');
                    }

                    // Trigger the main test stop function
                    if (window.stopTest) {
                        window.stopTest();
                    }
                });
            }

            // Mobile share and history buttons
            const mobileShareBtn = document.getElementById('mobile-share-btn');
            const mobileHistoryBtn = document.getElementById('mobile-history-btn');

            if (mobileShareBtn) {
                mobileShareBtn.addEventListener('click', function() {
                    // Trigger share functionality
                    if (window.shareResults) {
                        window.shareResults();
                    }
                });
            }

            if (mobileHistoryBtn) {
                mobileHistoryBtn.addEventListener('click', function() {
                    // Trigger history functionality
                    if (window.showHistory) {
                        window.showHistory();
                    }
                });
            }
        });
    </script>
</body>
</html>
    </header>
    
    <!-- Main Content Area -->
    <div class="container">

        <!-- Enhanced Mobile-First Hero Section -->
        <div class="relative overflow-hidden mt-6 md:mt-12 mb-8 md:mb-16 mx-4 md:mx-0">
            <!-- Hero Background -->
            <div class="absolute inset-0 cyber-card"></div>
            <div class="absolute inset-0 scan-line"></div>

            <div class="relative z-10 px-4 md:px-8 py-12 md:py-20 text-center">
                <!-- Mobile-Optimized Main Title -->
                <div class="mb-6 md:mb-8">
                    <h1 class="mobile-hero-title md:text-7xl font-black mb-4 leading-tight">
                        <span class="block bg-gradient-to-r from-primary-400 via-secondary-400 to-cyber-400 bg-clip-text text-transparent animate-float">
                            VOLUME
                        </span>
                        <span class="block bg-gradient-to-r from-cyber-400 via-primary-400 to-secondary-400 bg-clip-text text-transparent neon-text">
                            SHADER
                        </span>
                        <span class="block text-lg md:text-3xl font-mono text-gray-600 dark:text-gray-400 mt-2">
                            &gt; TESTING PROTOCOL
                        </span>
                    </h1>
                </div>

                <!-- Mobile-Optimized Subtitle -->
                <div class="mb-8 md:mb-12 max-w-4xl mx-auto">
                    <p class="mobile-hero-subtitle md:text-2xl text-gray-700 dark:text-gray-300 mb-4 md:mb-6 font-light leading-relaxed px-2">
                        Experience <span class="text-primary-400 font-semibold">cutting-edge GPU performance analysis</span> through
                        sophisticated volume shader rendering and real-time fractal computation.
                    </p>
                    <div class="grid mobile-stats-grid md:flex md:flex-wrap justify-center gap-2 md:gap-4 text-xs md:text-sm font-mono text-gray-500 dark:text-gray-400 px-2">
                        <span class="px-2 md:px-3 py-1 bg-primary-400/10 rounded-full border border-primary-400/20 text-center">WebGL 2.0</span>
                        <span class="px-2 md:px-3 py-1 bg-secondary-400/10 rounded-full border border-secondary-400/20 text-center">Ray Marching</span>
                        <span class="px-2 md:px-3 py-1 bg-cyber-400/10 rounded-full border border-cyber-400/20 text-center">Fractal Rendering</span>
                        <span class="px-2 md:px-3 py-1 bg-accent-400/10 rounded-full border border-accent-400/20 text-center">Real-time Analysis</span>
                    </div>
                </div>

                <!-- Mobile-Optimized CTA Buttons -->
                <div class="flex flex-col gap-4 md:gap-6 mb-8 md:mb-12 px-4 md:px-0">
                    <a href="#test" class="mobile-cta-button group relative bg-gradient-to-r from-primary-500 to-secondary-500 rounded-xl text-white font-semibold shadow-2xl hover:shadow-primary-500/25 transition-all duration-300 transform hover:scale-105">
                        <div class="absolute inset-0 bg-gradient-to-r from-primary-400 to-secondary-400 rounded-xl blur opacity-50 group-hover:opacity-75 transition-opacity duration-300"></div>
                        <span class="relative z-10 flex items-center justify-center">
                            <i class="fas fa-rocket mr-3 text-xl"></i>
                            INITIATE VOLUME TEST
                        </span>
                    </a>
                    <a href="#technical-details" class="mobile-cta-button group relative glass border border-white/20 rounded-xl font-semibold hover:bg-white/10 transition-all duration-300">
                        <span class="flex items-center justify-center text-gray-700 dark:text-gray-300">
                            <i class="fas fa-microscope mr-3 text-xl text-primary-400"></i>
                            TECHNICAL ANALYSIS
                        </span>
                    </a>
                </div>

                <!-- Mobile-Optimized Stats Grid -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-6 max-w-4xl mx-auto px-4 md:px-0">
                    <div class="glass rounded-lg p-3 md:p-4 border border-white/20 text-center hover-lift">
                        <div class="text-lg md:text-2xl font-bold text-primary-400 mb-1">WebGL 2.0</div>
                        <div class="text-xs md:text-sm text-gray-600 dark:text-gray-400">Graphics API</div>
                    </div>
                    <div class="glass rounded-lg p-3 md:p-4 border border-white/20 text-center hover-lift">
                        <div class="text-lg md:text-2xl font-bold text-secondary-400 mb-1">60+ FPS</div>
                        <div class="text-xs md:text-sm text-gray-600 dark:text-gray-400">Target Performance</div>
                    </div>
                    <div class="glass rounded-lg p-3 md:p-4 border border-white/20 text-center hover-lift">
                        <div class="text-lg md:text-2xl font-bold text-cyber-400 mb-1">1000+</div>
                        <div class="text-xs md:text-sm text-gray-600 dark:text-gray-400">Ray Steps</div>
                    </div>
                    <div class="glass rounded-lg p-3 md:p-4 border border-white/20 text-center hover-lift">
                        <div class="text-lg md:text-2xl font-bold text-accent-400 mb-1">16x</div>
                        <div class="text-xs md:text-sm text-gray-600 dark:text-gray-400">Max Complexity</div>
                    </div>
                </div>
            </div>
        </div>
