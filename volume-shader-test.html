<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Volume Shader Test - Advanced GPU Performance Analysis Tool</title>
    <meta name="description" content="Comprehensive volume shader testing platform for advanced GPU performance analysis, featuring real-time ray marching, fractal rendering, and detailed performance metrics.">
    <meta name="keywords" content="volume shader,ray marching,GPU test,WebGL,fractal rendering,performance analysis">
    <meta name="author" content="Toxic Mushroom Test Team">
    <link rel="canonical" href="https://volumeshaderbmtest.com/volume-shader-test.html">
    <link rel="icon" href="logo.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="logo.svg">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://volumeshaderbmtest.com/volume-shader-test.html">
    <meta property="og:title" content="Volume Shader Test - Advanced GPU Performance Analysis">
    <meta property="og:description" content="Professional volume shader testing platform with real-time ray marching and fractal rendering capabilities.">
    <meta property="og:image" content="https://volumeshaderbmtest.com/images/volume-shader-og.jpg">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://volumeshaderbmtest.com/volume-shader-test.html">
    <meta property="twitter:title" content="Volume Shader Test - Advanced GPU Performance Analysis">
    <meta property="twitter:description" content="Professional volume shader testing platform with real-time ray marching and fractal rendering capabilities.">
    <meta property="twitter:image" content="https://volumeshaderbmtest.com/images/volume-shader-twitter.jpg">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "Volume Shader Test",
        "applicationCategory": "UtilitiesApplication",
        "operatingSystem": "Web Browser",
        "url": "https://volumeshaderbmtest.com/volume-shader-test.html",
        "description": "Advanced volume shader testing platform featuring real-time ray marching, fractal rendering, and comprehensive GPU performance analysis with detailed metrics and visualization.",
        "featureList": [
            "Real-time Ray Marching",
            "Fractal Volume Rendering",
            "Advanced Shader Complexity Testing",
            "Performance Metrics Analysis",
            "Temperature Monitoring",
            "Stability Assessment",
            "Custom Parameter Control",
            "Export Test Results"
        ],
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
        },
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.9",
            "reviewCount": "850",
            "bestRating": "5",
            "worstRating": "1"
        }
    }
    </script>
    
    <script src="static/js/js.js"></script>
    <script src="static/js/chart.js"></script>
    <script src="static/js/three.min.js"></script>
    <link rel="stylesheet" href="static/css/all.min.css">
    <link href="static/css/css2.css" rel="stylesheet">
    <link rel="stylesheet" href="static/css/style.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                container: {
                    center: true,
                    padding: '1rem',
                    screens: {
                        sm: '640px',
                        md: '768px',
                        lg: '1024px',
                        xl: '1100px',
                        '2xl': '1100px'
                    },
                },
                extend: {
                    colors: {
                        primary: {
                            50: '#eef2ff',
                            100: '#e0e7ff',
                            200: '#c7d2fe',
                            300: '#a5b4fc',
                            400: '#818cf8',
                            500: '#6366f1',
                            600: '#4f46e5',
                            700: '#4338ca',
                            800: '#3730a3',
                            900: '#312e81',
                        },
                        secondary: {
                            50: '#f5f3ff',
                            100: '#ede9fe',
                            200: '#ddd6fe',
                            300: '#c4b5fd',
                            400: '#a78bfa',
                            500: '#8b5cf6',
                            600: '#7c3aed',
                            700: '#6d28d9',
                            800: '#5b21b6',
                            900: '#4c1d95',
                        },
                        danger: '#ef4444',
                        warning: '#f59e0b',
                        success: '#10b981',
                    },
                    fontFamily: {
                        sans: ['Noto Sans SC', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen font-sans text-gray-800 dark:text-gray-200">
    <!-- Background Decoration -->
    <div class="fixed inset-0 -z-10 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-indigo-200/30 via-transparent to-transparent dark:from-indigo-900/20"></div>
    
    <!-- Header Navigation Bar -->
    <header class="sticky top-0 z-50 shadow-sm">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <img src="logo.svg" alt="Logo" class="w-8 h-8">
                <h1 class="text-xl font-semibold">Volume Shader Test</h1>
            </div>
            <nav class="nav-menu hidden md:block">
                <ul class="flex flex-wrap gap-x-6 gap-y-2">
                    <li><a href="index.html" class="nav-link">Home</a></li>
                    <li><a href="#test" class="active nav-link">Volume Test</a></li>
                    <li><a href="#technical-details" class="nav-link">Technical Details</a></li>
                    <li><a href="#performance-guide" class="nav-link">Performance Guide</a></li>
                    <li><a href="#shader-analysis" class="nav-link">Shader Analysis</a></li>
                    <li><a href="#faq" class="nav-link">FAQ</a></li>
                </ul>
            </nav>
            <div class="flex items-center space-x-4">
                <div class="language-selector relative">
                    <button class="flex items-center space-x-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="language-toggle" title="Switch Language">
                        <i class="fas fa-globe"></i>
                        <span class="text-sm">English</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div class="language-dropdown absolute right-0 mt-2 w-40 bg-white dark:bg-gray-800 rounded-md shadow-lg overflow-hidden z-20 hidden">
                        <a href="volume-shader-test.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 bg-gray-100 dark:bg-gray-700">English</a>
                        <a href="zh-cn/volume-shader-test.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">中文</a>
                        <a href="ja-jp/volume-shader-test.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">日本語</a>
                    </div>
                </div>
                <div class="theme-toggle rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="theme-toggle" title="Switch Theme">
                    <i class="fas fa-moon"></i>
                </div>
                <div class="mobile-menu-btn md:hidden rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="mobile-menu-btn" title="Menu">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
        <!-- Mobile Navigation Menu (Hidden by default) -->
        <div class="mobile-nav hidden bg-white dark:bg-gray-900 shadow-md w-full absolute left-0 top-full border-t border-gray-200 dark:border-gray-700 py-3 px-4 md:hidden">
            <ul class="flex flex-col space-y-3">
                <li><a href="index.html" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Home</a></li>
                <li><a href="#test" class="active block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Volume Test</a></li>
                <li><a href="#technical-details" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Technical Details</a></li>
                <li><a href="#performance-guide" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Performance Guide</a></li>
                <li><a href="#shader-analysis" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Shader Analysis</a></li>
                <li><a href="#faq" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">FAQ</a></li>
            </ul>
        </div>

        <!-- Main Test Area -->
        <div id="test" class="card p-6 mb-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 3D Rendering Area -->
                <div class="lg:col-span-2">
                    <div class="mb-4 flex justify-between items-center">
                        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Volume Shader Renderer</h2>
                        <div class="flex space-x-2">
                            <button id="fullscreen-btn" class="btn-secondary text-sm px-3 py-1">
                                <i class="fas fa-expand mr-1"></i> Fullscreen
                            </button>
                            <button id="screenshot-btn" class="btn-secondary text-sm px-3 py-1">
                                <i class="fas fa-camera mr-1"></i> Screenshot
                            </button>
                        </div>
                    </div>

                    <!-- WebGL Rendering Canvas -->
                    <div id="canvas-container" class="relative bg-black rounded-lg overflow-hidden border border-gray-300 dark:border-gray-700" style="height: 400px;">
                        <canvas id="mushroom-canvas" class="w-full h-full"></canvas>

                        <!-- Rendering Status Overlay -->
                        <div id="render-overlay" class="absolute inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm">
                            <div class="text-center text-white">
                                <i class="fas fa-cube text-4xl mb-2 animate-pulse"></i>
                                <div class="hidden lg:block">Click the Start Test button to begin volume rendering</div>
                                <div class="lg:hidden">Click the button below to start volume shader test</div>
                                <div class="text-sm text-gray-300 mt-1">Advanced ray marching and fractal computation</div>
                            </div>
                        </div>

                        <!-- Mobile Test Controls Overlay -->
                        <div id="mobile-controls-overlay" class="absolute inset-0 bg-black/80 backdrop-blur-sm lg:hidden" style="display: none;">
                            <div class="flex flex-col h-full">
                                <!-- Close Button -->
                                <div class="flex justify-end p-3 flex-shrink-0">
                                    <button id="close-mobile-controls" class="text-white hover:text-gray-300 text-xl">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>

                                <!-- Content Area -->
                                <div class="flex-1 flex flex-col overflow-hidden">
                                    <!-- Title -->
                                    <div class="px-4 pb-3 flex-shrink-0">
                                        <h3 class="text-white font-semibold text-lg text-center">Select Volume Test Mode</h3>
                                    </div>

                                    <!-- Test Mode Selection - Scrollable Area -->
                                    <div class="flex-1 overflow-y-auto px-4">
                                        <div class="space-y-3">
                                            <div class="mobile-test-mode-card active" data-mode="light">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-blue-400 text-xl">
                                                        <i class="fas fa-feather"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">Basic Volume</h4>
                                                        <p class="text-gray-300 text-sm">Simple ray marching with low complexity</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="mobile-test-mode-card" data-mode="medium">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-yellow-400 text-xl">
                                                        <i class="fas fa-shield-alt"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">Standard Volume</h4>
                                                        <p class="text-gray-300 text-sm">Enhanced fractal rendering with medium complexity</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="mobile-test-mode-card" data-mode="heavy">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-orange-400 text-xl">
                                                        <i class="fas fa-fire"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">Advanced Volume</h4>
                                                        <p class="text-gray-300 text-sm">High-detail ray marching with complex shaders</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="mobile-test-mode-card" data-mode="extreme">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-red-400 text-xl">
                                                        <i class="fas fa-bomb"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">Extreme Volume</h4>
                                                        <p class="text-gray-300 text-sm">Maximum complexity fractal computation</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Test Control Buttons - Fixed at Bottom -->
                                    <div class="p-4 flex-shrink-0 space-y-3">
                                        <button id="mobile-start-test-btn" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 flex items-center justify-center shadow-lg">
                                            <i class="fas fa-play mr-2"></i>
                                            Start Volume Test
                                        </button>

                                        <button id="mobile-stop-test-btn" class="w-full bg-red-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-red-600 transition-all duration-200 flex items-center justify-center opacity-50 shadow-lg" disabled>
                                            <i class="fas fa-stop mr-2"></i>
                                            Stop Test
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Mobile Quick Start Button -->
                        <div id="mobile-quick-start" class="absolute bottom-4 left-4 right-4 lg:hidden">
                            <button id="show-mobile-controls" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 flex items-center justify-center shadow-lg">
                                <i class="fas fa-play mr-2"></i>
                                Start Volume Shader Test
                            </button>
                        </div>

                        <!-- FPS and Performance Information Overlay -->
                        <div id="performance-hud" class="absolute top-2 left-2 bg-black/70 backdrop-blur-sm rounded-lg p-2 text-white text-sm font-mono hidden">
                            <div class="grid grid-cols-2 gap-x-4 gap-y-1">
                                <div>FPS: <span id="fps-display" class="text-green-400">0</span></div>
                                <div>Ray Steps: <span id="render-time" class="text-blue-400">0</span></div>
                                <div>Iterations: <span id="triangle-count" class="text-yellow-400">0</span></div>
                                <div>Complexity: <span id="complexity-level" class="text-purple-400">1.0x</span></div>
                            </div>
                        </div>

                        <!-- Warning Message -->
                        <div id="warning-overlay" class="absolute bottom-2 left-2 right-2 bg-red-600/90 backdrop-blur-sm rounded-lg p-2 text-white text-sm hidden">
                            <i class="fas fa-exclamation-triangle mr-1"></i>
                            <span id="warning-text">Performance issue detected, consider lowering test level</span>
                        </div>
                    </div>

                    <!-- Device Information -->
                    <div class="mt-4 bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">System Information</h3>
                        <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                            <div class="flex justify-between">
                                <span>GPU:</span>
                                <span id="gpu-info" class="text-right">Detecting...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>WebGL Version:</span>
                                <span id="webgl-version" class="text-right">Detecting...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Browser:</span>
                                <span id="browser-info" class="text-right">Detecting...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Operating System:</span>
                                <span id="os-info" class="text-right">Detecting...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Control Panel and Results Area -->
                <div class="space-y-4 hidden lg:block">
                    <!-- Test Mode Selection Area -->
                    <div class="test-modes-container">
                            <a href="#" class="test-mode-card active" data-mode="light">
                                <div class="mode-icon">
                                    <i class="fas fa-feather"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Basic Volume</h3>
                                    <p>Simple ray marching</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="medium">
                                <div class="mode-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Standard Volume</h3>
                                    <p>Enhanced fractal rendering</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="heavy">
                                <div class="mode-icon">
                                    <i class="fas fa-fire"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Advanced Volume</h3>
                                    <p>High-detail ray marching</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="extreme">
                                <div class="mode-icon">
                                    <i class="fas fa-bomb"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Extreme Volume</h3>
                                    <p>Maximum complexity</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="custom">
                                <div class="mode-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Custom</h3>
                                    <p>Custom parameters</p>
                                </div>
                            </a>
                        </div>

                    <!-- Test Controls -->
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">Volume Test Controls</h3>

                        <!-- Test Buttons -->
                        <div class="grid grid-cols-2 gap-2 mb-4">
                            <button id="start-test-btn" class="btn-primary flex items-center justify-center h-10">
                                <i class="fas fa-play mr-2"></i> Start Test
                            </button>
                            <button id="stop-test-btn" class="btn-danger flex items-center justify-center h-10 opacity-50" disabled>
                                <i class="fas fa-stop mr-2"></i> Stop Test
                            </button>
                        </div>

                        <!-- Test Progress -->
                        <div id="test-progress" class="hidden mb-4">
                            <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                <span>Test Progress</span>
                                <span id="progress-text">0/60 seconds</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div id="progress-bar" class="bg-gradient-to-r from-primary-500 to-secondary-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                        </div>

                        <!-- Custom Parameters -->
                        <div id="custom-params" class="space-y-3 hidden">
                            <div>
                                <label for="triangle-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Ray Steps</label>
                                <input type="range" id="triangle-slider" min="100" max="2000" value="800" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Current: <span id="triangle-value">800</span></div>
                            </div>
                            <div>
                                <label for="complexity-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Fractal Iterations</label>
                                <input type="range" id="complexity-slider" min="2" max="16" step="1" value="8" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Current: <span id="complexity-value">8</span></div>
                            </div>
                            <div>
                                <label for="duration-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Test Duration (seconds)</label>
                                <input type="range" id="duration-slider" min="10" max="300" value="60" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Current: <span id="duration-value">60</span> seconds</div>
                            </div>
                        </div>
                    </div>

                    <!-- Current Performance Metrics -->
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">Real-time Performance</h3>
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600 dark:text-green-400" id="current-fps">0</div>
                                <div class="text-gray-500 dark:text-gray-400">FPS</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400" id="current-score">0</div>
                                <div class="text-gray-500 dark:text-gray-400">Score</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-yellow-600 dark:text-yellow-400" id="current-temp">Normal</div>
                                <div class="text-gray-500 dark:text-gray-400">Temperature Status</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-purple-600 dark:text-purple-400" id="current-level">Basic Volume</div>
                                <div class="text-gray-500 dark:text-gray-400">Test Level</div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="grid grid-cols-2 gap-2">
                        <button id="share-btn" class="btn-secondary flex items-center justify-center h-10">
                            <i class="fas fa-share-alt mr-2"></i> Share Results
                        </button>
                        <button id="history-btn" class="btn-secondary flex items-center justify-center h-10">
                            <i class="fas fa-history mr-2"></i> Test History
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Charts and Test History -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Performance Chart Area -->
            <div class="card p-6 mb-0 flex flex-col" style="height: 400px;">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Volume Performance Analysis</h2>
                    <div class="flex space-x-2">
                        <button class="chart-tab active" data-chart="fps">FPS Chart</button>
                        <button class="chart-tab" data-chart="score">Score Trend</button>
                    </div>
                </div>
                <div class="chart-content flex-grow">
                    <div class="chart-wrapper h-full" id="fps-chart-wrapper" style="display:block;">
                        <canvas id="fpsChart" class="w-full h-full"></canvas>
                    </div>
                    <div class="chart-wrapper h-full" id="score-chart-wrapper" style="display:none;">
                        <canvas id="scoreChart" class="w-full h-full"></canvas>
                    </div>
                </div>
                <div class="mt-4 grid grid-cols-2 sm:grid-cols-4 gap-2 text-center">
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">Average FPS</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="avg-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">Maximum FPS</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="max-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">Minimum FPS</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="min-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">Stability</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="stability">-</div>
                    </div>
                </div>
            </div>

            <!-- Test History Area -->
            <div class="card p-6 mb-0 flex flex-col" style="height: 400px;">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Volume Test History</h2>
                    <button class="clear-history-btn btn-danger text-sm py-1 px-3">
                        <i class="fas fa-trash-alt mr-1"></i> Clear
                    </button>
                </div>
                <div class="history-log flex-grow overflow-y-auto bg-white/50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="empty-log p-4 text-center text-gray-500 dark:text-gray-400 flex flex-col items-center justify-center h-full">
                        <i class="fas fa-chart-line text-4xl mb-2 opacity-30"></i>
                        <p>No volume test records yet</p>
                        <p class="text-sm">Completed volume shader tests will appear here</p>
                    </div>
                    <div id="history-items" class="hidden max-h-full overflow-y-auto">
                        <!-- History items will be dynamically added via JavaScript -->
                    </div>
                </div>
                <div class="mt-4 flex justify-between">
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        Total <span id="history-count">0</span> tests
                    </div>
                    <div class="flex space-x-2">
                        <button id="export-history" class="btn-primary text-sm py-1 px-3 flex items-center">
                            <i class="fas fa-download mr-1"></i> Export
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technical Details Section -->
        <div id="technical-details" class="card p-8 mt-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 dark:text-gray-200">Technical Implementation Details</h2>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-xl font-semibold mb-4 text-primary-600 dark:text-primary-400">Volume Rendering Technology</h3>
                    <div class="space-y-4 text-gray-700 dark:text-gray-300">
                        <p>
                            Our volume shader test utilizes advanced <strong>ray marching algorithms</strong> to render complex
                            three-dimensional fractal structures in real-time. This technique involves casting rays from the
                            camera through each pixel and sampling the volume at regular intervals to determine color and opacity.
                        </p>
                        <p>
                            The core rendering pipeline implements a sophisticated <strong>distance field evaluation system</strong>
                            that calculates the signed distance to the fractal surface at any point in 3D space. This allows for
                            precise surface detection and enables high-quality lighting calculations.
                        </p>
                        <p>
                            Our fractal kernel uses an <strong>iterative escape-time algorithm</strong> with configurable complexity
                            levels, allowing the test to scale from simple geometric forms to highly detailed mathematical structures
                            that stress-test modern GPU architectures.
                        </p>
                    </div>
                </div>

                <div>
                    <h3 class="text-xl font-semibold mb-4 text-primary-600 dark:text-primary-400">Shader Architecture</h3>
                    <div class="space-y-4 text-gray-700 dark:text-gray-300">
                        <p>
                            The volume shader is implemented using <strong>WebGL 2.0 fragment shaders</strong> with high-precision
                            floating-point calculations. The shader code is optimized for parallel execution across GPU cores,
                            maximizing throughput and testing the graphics card's computational capabilities.
                        </p>
                        <p>
                            Key shader features include <strong>adaptive step sizing</strong> for ray marching optimization,
                            <strong>normal vector calculation</strong> using finite differences for realistic lighting, and
                            <strong>reflection mapping</strong> for enhanced visual quality and increased computational load.
                        </p>
                        <p>
                            The test dynamically adjusts shader complexity through uniform variables, allowing real-time
                            modification of fractal iteration counts, ray step sizes, and mathematical precision levels
                            to provide comprehensive GPU performance analysis.
                        </p>
                    </div>
                </div>
            </div>

            <div class="mt-8 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-6">
                <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Performance Metrics Explained</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-medium text-primary-600 dark:text-primary-400 mb-2">Frame Rate (FPS)</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            Measures the number of complete frames rendered per second. Higher FPS indicates better GPU performance
                            and smoother visual experience. Our test monitors FPS continuously to detect performance degradation.
                        </p>
                    </div>
                    <div>
                        <h4 class="font-medium text-primary-600 dark:text-primary-400 mb-2">Ray Marching Steps</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            The number of samples taken along each ray to determine surface intersection. More steps provide
                            higher accuracy but require more computational power, making this a key performance indicator.
                        </p>
                    </div>
                    <div>
                        <h4 class="font-medium text-primary-600 dark:text-primary-400 mb-2">Fractal Iterations</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            The number of mathematical iterations performed to generate the fractal structure. Higher iteration
                            counts produce more detailed fractals but significantly increase computational complexity.
                        </p>
                    </div>
                    <div>
                        <h4 class="font-medium text-primary-600 dark:text-primary-400 mb-2">Stability Score</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            Measures frame rate consistency over time. A high stability score indicates reliable performance
                            without significant frame drops, which is crucial for smooth real-time applications.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Guide Section -->
        <div id="performance-guide" class="card p-8 mt-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 dark:text-gray-200">Volume Shader Performance Guide</h2>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div class="lg:col-span-2">
                    <h3 class="text-xl font-semibold mb-4 text-primary-600 dark:text-primary-400">Optimizing Your Volume Shader Experience</h3>

                    <div class="space-y-6">
                        <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                            <h4 class="font-semibold text-lg mb-3 text-gray-800 dark:text-gray-200">Pre-Test Preparation</h4>
                            <ul class="space-y-2 text-gray-700 dark:text-gray-300">
                                <li class="flex items-start space-x-2">
                                    <i class="fas fa-check-circle text-green-500 mt-1"></i>
                                    <span>Close unnecessary applications and browser tabs to free up system resources</span>
                                </li>
                                <li class="flex items-start space-x-2">
                                    <i class="fas fa-check-circle text-green-500 mt-1"></i>
                                    <span>Ensure your device has adequate cooling and ventilation</span>
                                </li>
                                <li class="flex items-start space-x-2">
                                    <i class="fas fa-check-circle text-green-500 mt-1"></i>
                                    <span>Update your graphics drivers to the latest version</span>
                                </li>
                                <li class="flex items-start space-x-2">
                                    <i class="fas fa-check-circle text-green-500 mt-1"></i>
                                    <span>Enable hardware acceleration in your browser settings</span>
                                </li>
                            </ul>
                        </div>

                        <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                            <h4 class="font-semibold text-lg mb-3 text-gray-800 dark:text-gray-200">Test Mode Selection Guidelines</h4>
                            <div class="space-y-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                        <i class="fas fa-feather text-blue-600 dark:text-blue-400 text-sm"></i>
                                    </div>
                                    <div>
                                        <strong class="text-gray-800 dark:text-gray-200">Basic Volume:</strong>
                                        <span class="text-gray-600 dark:text-gray-400">Ideal for integrated graphics, mobile devices, or initial performance assessment</span>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center">
                                        <i class="fas fa-shield-alt text-yellow-600 dark:text-yellow-400 text-sm"></i>
                                    </div>
                                    <div>
                                        <strong class="text-gray-800 dark:text-gray-200">Standard Volume:</strong>
                                        <span class="text-gray-600 dark:text-gray-400">Suitable for mid-range dedicated graphics cards and comprehensive testing</span>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center">
                                        <i class="fas fa-fire text-orange-600 dark:text-orange-400 text-sm"></i>
                                    </div>
                                    <div>
                                        <strong class="text-gray-800 dark:text-gray-200">Advanced Volume:</strong>
                                        <span class="text-gray-600 dark:text-gray-400">Designed for high-end graphics cards and professional workstations</span>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                                        <i class="fas fa-bomb text-red-600 dark:text-red-400 text-sm"></i>
                                    </div>
                                    <div>
                                        <strong class="text-gray-800 dark:text-gray-200">Extreme Volume:</strong>
                                        <span class="text-gray-600 dark:text-gray-400">Maximum stress test for flagship GPUs and thermal limit testing</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                            <h4 class="font-semibold text-lg mb-3 text-gray-800 dark:text-gray-200">Interpreting Test Results</h4>
                            <div class="space-y-3 text-gray-700 dark:text-gray-300">
                                <p>
                                    <strong>Excellent Performance (60+ FPS):</strong> Your GPU handles volume shaders exceptionally well.
                                    Consider trying higher complexity levels or using this configuration for demanding 3D applications.
                                </p>
                                <p>
                                    <strong>Good Performance (30-60 FPS):</strong> Solid performance suitable for most volume rendering tasks.
                                    This indicates a well-balanced system capable of handling moderate computational loads.
                                </p>
                                <p>
                                    <strong>Moderate Performance (15-30 FPS):</strong> Acceptable for basic volume rendering but may struggle
                                    with complex scenes. Consider optimizing settings or upgrading hardware for better performance.
                                </p>
                                <p>
                                    <strong>Poor Performance (&lt;15 FPS):</strong> Indicates hardware limitations or system bottlenecks.
                                    Try lower complexity settings or check for driver updates and system optimization opportunities.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 class="text-xl font-semibold mb-4 text-primary-600 dark:text-primary-400">Performance Tips</h3>
                    <div class="space-y-4">
                        <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800">
                            <h4 class="font-medium text-green-800 dark:text-green-200 mb-2">
                                <i class="fas fa-lightbulb mr-2"></i>Browser Optimization
                            </h4>
                            <ul class="text-sm text-green-700 dark:text-green-300 space-y-1">
                                <li>• Use Chrome or Firefox for best WebGL support</li>
                                <li>• Enable hardware acceleration</li>
                                <li>• Close other GPU-intensive tabs</li>
                                <li>• Disable browser extensions temporarily</li>
                            </ul>
                        </div>

                        <div class="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                            <h4 class="font-medium text-blue-800 dark:text-blue-200 mb-2">
                                <i class="fas fa-cogs mr-2"></i>System Settings
                            </h4>
                            <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                                <li>• Set power plan to "High Performance"</li>
                                <li>• Ensure adequate system cooling</li>
                                <li>• Close background applications</li>
                                <li>• Monitor system temperatures</li>
                            </ul>
                        </div>

                        <div class="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-800">
                            <h4 class="font-medium text-purple-800 dark:text-purple-200 mb-2">
                                <i class="fas fa-chart-line mr-2"></i>Monitoring Tools
                            </h4>
                            <ul class="text-sm text-purple-700 dark:text-purple-300 space-y-1">
                                <li>• Watch real-time FPS counter</li>
                                <li>• Monitor temperature warnings</li>
                                <li>• Check stability percentage</li>
                                <li>• Review performance history</li>
                            </ul>
                        </div>

                        <div class="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-lg p-4 border border-orange-200 dark:border-orange-800">
                            <h4 class="font-medium text-orange-800 dark:text-orange-200 mb-2">
                                <i class="fas fa-exclamation-triangle mr-2"></i>Safety Precautions
                            </h4>
                            <ul class="text-sm text-orange-700 dark:text-orange-300 space-y-1">
                                <li>• Stop test if temperatures exceed safe limits</li>
                                <li>• Don't run extreme tests on older hardware</li>
                                <li>• Allow cooling time between tests</li>
                                <li>• Monitor for system instability</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Shader Analysis Section -->
        <div id="shader-analysis" class="card p-8 mt-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 dark:text-gray-200">Advanced Shader Analysis</h2>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-xl font-semibold mb-4 text-primary-600 dark:text-primary-400">Ray Marching Algorithm</h3>
                    <div class="bg-gray-900 rounded-lg p-4 mb-4 overflow-x-auto">
                        <pre class="text-green-400 text-sm"><code>// Core ray marching loop
for(int k = 2; k < max_steps; k++) {
    vec3 pos = origin + dir * (step_size * float(k));
    float distance = fractalKernel(pos);

    if(distance > 0.0 && prev_distance < 0.0) {
        // Surface intersection detected
        vec3 surface_point = refineSurfacePosition(pos);
        vec3 normal = calculateNormal(surface_point);
        color = computeLighting(surface_point, normal);
        break;
    }
    prev_distance = distance;
}</code></pre>
                    </div>
                    <p class="text-gray-700 dark:text-gray-300 mb-4">
                        The ray marching algorithm steps through 3D space along viewing rays, sampling the fractal
                        distance field at regular intervals. When a sign change is detected, indicating surface
                        intersection, the algorithm refines the position using binary search for sub-pixel accuracy.
                    </p>

                    <h4 class="font-semibold text-lg mb-2 text-gray-800 dark:text-gray-200">Key Algorithm Features:</h4>
                    <ul class="space-y-2 text-gray-700 dark:text-gray-300">
                        <li class="flex items-start space-x-2">
                            <i class="fas fa-arrow-right text-primary-500 mt-1"></i>
                            <span><strong>Adaptive Step Sizing:</strong> Dynamically adjusts step size based on complexity level</span>
                        </li>
                        <li class="flex items-start space-x-2">
                            <i class="fas fa-arrow-right text-primary-500 mt-1"></i>
                            <span><strong>Binary Search Refinement:</strong> Achieves sub-pixel surface accuracy</span>
                        </li>
                        <li class="flex items-start space-x-2">
                            <i class="fas fa-arrow-right text-primary-500 mt-1"></i>
                            <span><strong>Early Termination:</strong> Optimizes performance by stopping unnecessary calculations</span>
                        </li>
                        <li class="flex items-start space-x-2">
                            <i class="fas fa-arrow-right text-primary-500 mt-1"></i>
                            <span><strong>Golden Section Search:</strong> Efficiently finds optimal surface points</span>
                        </li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-xl font-semibold mb-4 text-primary-600 dark:text-primary-400">Fractal Mathematics</h3>
                    <div class="bg-gray-900 rounded-lg p-4 mb-4 overflow-x-auto">
                        <pre class="text-blue-400 text-sm"><code>// Fractal kernel computation
float fractalKernel(vec3 pos) {
    vec3 z = pos;
    float r, theta, phi;

    for(int i = 0; i < iterations; i++) {
        r = length(z);
        if(r > escape_radius) break;

        theta = atan(z.y, z.x) * power;
        phi = acos(clamp(z.z / r, -1.0, 1.0)) * power;
        r = pow(r, power);

        z = vec3(r * sin(phi) * cos(theta),
                 r * sin(phi) * sin(theta),
                 r * cos(phi)) + pos;
    }

    return escape_radius - length(z);
}</code></pre>
                    </div>
                    <p class="text-gray-700 dark:text-gray-300 mb-4">
                        The fractal kernel implements a 3D extension of the Mandelbrot set using spherical coordinates.
                        Each iteration transforms the current point through a power function, creating complex self-similar
                        structures that challenge GPU computational capabilities.
                    </p>

                    <h4 class="font-semibold text-lg mb-2 text-gray-800 dark:text-gray-200">Mathematical Properties:</h4>
                    <ul class="space-y-2 text-gray-700 dark:text-gray-300">
                        <li class="flex items-start space-x-2">
                            <i class="fas fa-infinity text-secondary-500 mt-1"></i>
                            <span><strong>Self-Similarity:</strong> Fractal patterns repeat at different scales</span>
                        </li>
                        <li class="flex items-start space-x-2">
                            <i class="fas fa-infinity text-secondary-500 mt-1"></i>
                            <span><strong>Escape-Time Algorithm:</strong> Determines fractal membership through iteration</span>
                        </li>
                        <li class="flex items-start space-x-2">
                            <i class="fas fa-infinity text-secondary-500 mt-1"></i>
                            <span><strong>Power Scaling:</strong> Configurable exponent affects fractal complexity</span>
                        </li>
                        <li class="flex items-start space-x-2">
                            <i class="fas fa-infinity text-secondary-500 mt-1"></i>
                            <span><strong>Spherical Mapping:</strong> 3D coordinate transformation for volume rendering</span>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="mt-8 bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg p-6">
                <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Performance Impact Analysis</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-microchip text-red-600 dark:text-red-400 text-2xl"></i>
                        </div>
                        <h4 class="font-semibold text-gray-800 dark:text-gray-200 mb-2">Computational Intensity</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            Each pixel requires hundreds of mathematical operations, including trigonometric functions,
                            power calculations, and vector operations, creating significant GPU load.
                        </p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-memory text-yellow-600 dark:text-yellow-400 text-2xl"></i>
                        </div>
                        <h4 class="font-semibold text-gray-800 dark:text-gray-200 mb-2">Memory Bandwidth</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            High-precision floating-point calculations and frequent memory access patterns test
                            the GPU's memory subsystem and cache efficiency.
                        </p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-thermometer-half text-green-600 dark:text-green-400 text-2xl"></i>
                        </div>
                        <h4 class="font-semibold text-gray-800 dark:text-gray-200 mb-2">Thermal Characteristics</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            Sustained high computational load generates significant heat, making this test ideal
                            for thermal throttling detection and cooling system evaluation.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div id="faq" class="card p-8 mt-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800 dark:text-gray-200">Frequently Asked Questions</h2>

            <div class="space-y-6">
                <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">
                        <i class="fas fa-question-circle text-primary-500 mr-2"></i>
                        What makes volume shader testing different from traditional GPU benchmarks?
                    </h3>
                    <p class="text-gray-700 dark:text-gray-300">
                        Volume shader testing focuses specifically on ray marching and fractal computation capabilities,
                        which are increasingly important for modern 3D applications, scientific visualization, and
                        real-time rendering. Unlike traditional polygon-based benchmarks, volume shaders test the GPU's
                        ability to handle complex mathematical operations and high-precision floating-point calculations
                        in parallel across thousands of cores.
                    </p>
                </div>

                <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">
                        <i class="fas fa-question-circle text-primary-500 mr-2"></i>
                        Why does my device get hot during volume shader testing?
                    </h3>
                    <p class="text-gray-700 dark:text-gray-300">
                        Volume shader rendering is computationally intensive, requiring the GPU to perform complex
                        mathematical calculations for every pixel on the screen. This sustained high workload causes
                        the GPU to operate at maximum capacity, generating significant heat. This is normal behavior
                        and actually helps evaluate your system's thermal management capabilities. If temperatures
                        become excessive, the test will display warnings and you should consider stopping the test
                        or reducing the complexity level.
                    </p>
                </div>

                <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">
                        <i class="fas fa-question-circle text-primary-500 mr-2"></i>
                        How do I interpret my volume shader test scores?
                    </h3>
                    <p class="text-gray-700 dark:text-gray-300 mb-3">
                        Test scores are calculated based on average frame rate, test complexity, and performance
                        stability. Here's how to interpret your results:
                    </p>
                    <ul class="space-y-2 text-gray-700 dark:text-gray-300 ml-4">
                        <li><strong>Score 1000+:</strong> Excellent performance, suitable for professional 3D work and high-end gaming</li>
                        <li><strong>Score 500-1000:</strong> Good performance, handles most volume rendering tasks well</li>
                        <li><strong>Score 200-500:</strong> Moderate performance, suitable for basic 3D applications</li>
                        <li><strong>Score &lt;200:</strong> Limited performance, may struggle with complex volume rendering</li>
                    </ul>
                </div>

                <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">
                        <i class="fas fa-question-circle text-primary-500 mr-2"></i>
                        What should I do if the test shows "WebGL not supported" error?
                    </h3>
                    <p class="text-gray-700 dark:text-gray-300">
                        This error indicates that your browser or system doesn't support WebGL, which is required
                        for volume shader testing. Try the following solutions: 1) Update your browser to the latest
                        version, 2) Enable hardware acceleration in browser settings, 3) Update your graphics drivers,
                        4) Try a different browser (Chrome and Firefox have the best WebGL support), 5) Check if your
                        graphics card supports OpenGL 3.0 or higher.
                    </p>
                </div>

                <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">
                        <i class="fas fa-question-circle text-primary-500 mr-2"></i>
                        Can I use custom parameters to test specific scenarios?
                    </h3>
                    <p class="text-gray-700 dark:text-gray-300">
                        Yes! Select the "Custom" test mode to access advanced parameters including ray marching steps,
                        fractal iteration count, and test duration. This allows you to create specific test scenarios
                        tailored to your needs, whether you're evaluating performance for a particular application
                        or conducting detailed GPU analysis. Custom parameters are particularly useful for developers
                        working with volume rendering applications.
                    </p>
                </div>

                <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">
                        <i class="fas fa-question-circle text-primary-500 mr-2"></i>
                        Is my test data stored or transmitted anywhere?
                    </h3>
                    <p class="text-gray-700 dark:text-gray-300">
                        All volume shader test data is processed and stored locally in your browser. No performance
                        data, system information, or test results are transmitted to external servers. Your privacy
                        is completely protected, and you can safely conduct tests without concerns about data collection.
                        Test history is saved in your browser's local storage and can be cleared at any time using
                        the "Clear History" button.
                    </p>
                </div>

                <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">
                        <i class="fas fa-question-circle text-primary-500 mr-2"></i>
                        How often should I run volume shader tests?
                    </h3>
                    <p class="text-gray-700 dark:text-gray-300">
                        For general users, running tests monthly or after system changes (driver updates, hardware
                        modifications) is sufficient. Professionals working with 3D applications might benefit from
                        weekly testing to monitor performance trends. Avoid running extreme tests repeatedly in short
                        periods, as this can cause unnecessary thermal stress. Always allow adequate cooling time
                        between intensive test sessions.
                    </p>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="mt-12 py-8 border-t border-gray-200 dark:border-gray-700">
            <div class="text-center">
                <div class="flex justify-center items-center space-x-2 mb-4">
                    <img src="logo.svg" alt="Logo" class="w-6 h-6">
                    <span class="text-lg font-semibold text-gray-800 dark:text-gray-200">Volume Shader Test</span>
                </div>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                    Advanced GPU performance analysis through sophisticated volume shader rendering
                </p>
                <div class="flex justify-center space-x-6 text-sm text-gray-500 dark:text-gray-400">
                    <a href="index.html" class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Home</a>
                    <a href="#technical-details" class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Technical Details</a>
                    <a href="#performance-guide" class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Performance Guide</a>
                    <a href="#faq" class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors">FAQ</a>
                </div>
                <div class="mt-4 text-xs text-gray-400 dark:text-gray-500">
                    © 2024 Toxic Mushroom Test Team. Advanced volume shader testing platform.
                </div>
            </div>
        </footer>
    </div>

    <!-- JavaScript -->
    <script src="static/js/app.js"></script>
</body>
</html>
    </header>
    
    <!-- Main Content Area -->
    <div class="container">
        
        <!-- Hero Section -->
        <div class="card p-8 mb-8 text-center">
            <h1 class="text-4xl font-bold mb-4 bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
                Advanced Volume Shader Testing Platform
            </h1>
            <p class="text-xl text-gray-600 dark:text-gray-300 mb-6 max-w-3xl mx-auto">
                Experience cutting-edge GPU performance analysis through sophisticated volume shader rendering, 
                real-time ray marching algorithms, and comprehensive fractal computation testing.
            </p>
            <div class="flex flex-wrap justify-center gap-4">
                <a href="#test" class="btn-primary px-6 py-3 text-lg">
                    <i class="fas fa-play mr-2"></i>Start Volume Test
                </a>
                <a href="#technical-details" class="btn-secondary px-6 py-3 text-lg">
                    <i class="fas fa-info-circle mr-2"></i>Learn More
                </a>
            </div>
        </div>
